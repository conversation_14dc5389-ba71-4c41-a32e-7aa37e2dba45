import 'dart:convert';
import 'package:seatrace/models/client.dart';
import 'package:seatrace/models/pecheur.dart';
import 'package:seatrace/models/veterinaire.dart';
import 'package:seatrace/models/maryeur.dart';
import 'package:seatrace/services/unified_api_service.dart';
import 'package:seatrace/models/admin.dart';
import 'package:seatrace/services/model_service.dart';
import 'package:seatrace/services/unified_notification_service.dart';
import 'package:seatrace/services/websocket_service.dart';
import 'package:seatrace/services/service_config.dart';
import 'package:seatrace/services/network_discovery_service.dart';
import 'package:seatrace/utils/error_handler.dart';
import 'package:seatrace/utils/app_error.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service d'authentification unifié
class UnifiedAuthService {
  static final UnifiedAuthService _instance = UnifiedAuthService._internal();
  factory UnifiedAuthService() => _instance;
  UnifiedAuthService._internal();

  static const String _userKey = 'current_user';
  static const String _userTypeKey = 'current_user_type';
  static const String _tokenKey = 'auth_token';

  final ModelService _modelService = ModelService();
  final UnifiedApiService _apiService = UnifiedApiService();

  /// Obtenir l'utilisateur actuel
  Future<dynamic> getCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(_tokenKey);

      if (token == null) {
        ErrorHandler.instance.logInfo(
          'Aucun token d\'authentification trouvé',
          context: 'UnifiedAuthService.getCurrentUser',
        );
        return null;
      }

      // Définir le token dans le service API
      _apiService.setAuthToken(token);

      // Journaliser l'action
      ErrorHandler.instance.logInfo(
        'Récupération de l\'utilisateur actuel',
        context: 'UnifiedAuthService',
      );

      // Essayer d'abord de récupérer l'utilisateur depuis les préférences locales
      final userJson = prefs.getString(_userKey);
      if (userJson != null) {
        try {
          final userMap = json.decode(userJson) as Map<String, dynamic>;
          final userType = prefs.getString(_userTypeKey) ?? 'client';

          // Créer l'objet utilisateur à partir des données locales
          dynamic localUser;
          switch (userType) {
            case 'pecheur':
              localUser = Pecheur.fromMap(userMap);
              break;
            case 'veterinaire':
              localUser = Veterinaire.fromMap(userMap);
              break;
            case 'maryeur':
              localUser = Maryeur.fromMap(userMap);
              break;
            case 'admin':
              localUser = Admin.fromJson(userMap);
              break;
            default:
              localUser = Client.fromMap(userMap);
          }

          ErrorHandler.instance.logInfo(
            'Utilisateur récupéré depuis le stockage local: ${_getUserName(localUser)}',
            context: 'UnifiedAuthService.getCurrentUser',
          );

          // Récupérer les données à jour depuis le serveur en arrière-plan
          _refreshUserData();

          return localUser;
        } catch (e) {
          ErrorHandler.instance.logWarning(
            'Erreur lors de la récupération de l\'utilisateur local: $e',
            context: 'UnifiedAuthService.getCurrentUser',
          );
          // Continuer pour récupérer depuis l'API
        }
      }

      // Récupérer l'utilisateur depuis l'API
      final user = await _apiService.getCurrentUser();

      if (user != null) {
        // Sauvegarder l'utilisateur dans les préférences
        await saveCurrentUser(user);

        // Vérifier si les informations de base sont présentes
        if (_getUserName(user).isEmpty) {
          ErrorHandler.instance.logWarning(
            'Informations utilisateur incomplètes',
            context: 'UnifiedAuthService.getCurrentUser',
          );
        } else {
          ErrorHandler.instance.logInfo(
            'Utilisateur récupéré avec succès depuis l\'API: ${_getUserName(user)}',
            context: 'UnifiedAuthService.getCurrentUser',
          );
        }

        return user;
      } else {
        ErrorHandler.instance.logWarning(
          'Aucun utilisateur récupéré depuis l\'API',
          context: 'UnifiedAuthService.getCurrentUser',
        );
        return null;
      }
    } on AppError catch (e) {
      // Si c'est une erreur d'authentification, déconnecter l'utilisateur
      if (e.type == ErrorType.authentication ||
          e.type == ErrorType.authorization) {
        ErrorHandler.instance.logWarning(
          'Session expirée, déconnexion de l\'utilisateur',
          context: 'UnifiedAuthService',
        );
        await logout();
      } else {
        ErrorHandler.instance.logError(
          e,
          context: 'UnifiedAuthService.getCurrentUser',
        );
      }
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedAuthService.getCurrentUser',
      );
      // Ne pas déconnecter automatiquement en cas d'erreur réseau
      // pour permettre l'utilisation hors ligne
    }
    return null;
  }

  /// Rafraîchit les données utilisateur depuis l'API en arrière-plan
  Future<void> _refreshUserData() async {
    try {
      final user = await _apiService.getCurrentUser();
      if (user != null) {
        await saveCurrentUser(user);
        ErrorHandler.instance.logInfo(
          'Données utilisateur rafraîchies en arrière-plan',
          context: 'UnifiedAuthService._refreshUserData',
        );
      }
    } catch (e) {
      ErrorHandler.instance.logWarning(
        'Échec du rafraîchissement des données utilisateur: $e',
        context: 'UnifiedAuthService._refreshUserData',
      );
    }
  }

  /// Obtenir le nom complet de l'utilisateur
  String _getUserName(dynamic user) {
    if (user is Pecheur ||
        user is Veterinaire ||
        user is Maryeur ||
        user is Client ||
        user is Admin) {
      return '${user.prenom} ${user.nom}';
    }
    return '';
  }

  /// Sauvegarder l'utilisateur actuel
  Future<void> saveCurrentUser(dynamic user) async {
    final prefs = await SharedPreferences.getInstance();
    final userMap = _modelService.userToMap(user);
    await prefs.setString(_userKey, json.encode(userMap));

    // Sauvegarder le type d'utilisateur
    String userType = 'client';
    if (user is Pecheur) {
      userType = 'pecheur';
    } else if (user is Veterinaire) {
      userType = 'veterinaire';
    } else if (user is Maryeur) {
      userType = 'maryeur';
    } else if (user is Admin) {
      userType = 'admin';
    }

    await prefs.setString(_userTypeKey, userType);

    // Initialiser le service WebSocket
    WebSocketService().initialize();

    // Initialiser le service de notification
    UnifiedNotificationService().initialize();
  }

  /// Déconnexion
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
    await prefs.remove(_userTypeKey);
    await prefs.remove(_tokenKey);
    await _apiService.logout();
  }

  /// Connexion
  Future<dynamic> login(String email, String password) async {
    try {
      // Journaliser l'action
      ErrorHandler.instance.logInfo(
        'Tentative de connexion: $email',
        context: 'UnifiedAuthService',
      );

      // Vérifier si le mode test est activé
      if (ServiceConfig().enableTestMode) {
        ErrorHandler.instance.logInfo(
          'Mode test activé, utilisation de données de test pour la connexion',
          context: 'UnifiedAuthService.login',
        );

        // Créer un token de test
        final token = 'test_token_${DateTime.now().millisecondsSinceEpoch}';
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_tokenKey, token);
        _apiService.setAuthToken(token);

        // Créer un utilisateur de test en fonction de l'email
        Map<String, dynamic> userData = {
          'id': '1',
          'email': email,
          'nom': 'Utilisateur',
          'prenom': 'Test',
          'telephone': '29056566',
          'adresse': 'Adresse de test',
          'ville': 'Tunis',
          'pays': 'Tunisie',
          'photo': '',
          'dateCreation': DateTime.now().toIso8601String(),
        };

        // Déterminer le type d'utilisateur en fonction de l'email
        String roles = 'client';
        if (email.contains('pecheur')) {
          roles = 'pecheur';
          userData['bateau'] = 'Bateau de test';
          userData['licence'] = 'LIC-12345';
        } else if (email.contains('veterinaire')) {
          roles = 'veterinaire';
          userData['certification'] = 'CERT-12345';
          userData['specialite'] = 'Poissons marins';
        } else if (email.contains('maryeur')) {
          roles = 'maryeur';
          userData['entreprise'] = 'Entreprise de test';
          userData['registreCommerce'] = 'RC-12345';
        }

        userData['roles'] = roles;

        // Créer l'objet utilisateur
        final user = _modelService.createUserFromMap(
          _modelService.standardizeMap(userData),
        );

        await saveCurrentUser(user);

        // Journaliser le succès
        ErrorHandler.instance.logInfo(
          'Connexion en mode test réussie: $email',
          context: 'UnifiedAuthService',
        );

        return user;
      }

      // Mode normal - Vérifier la connectivité au serveur avec plus de détails
      bool isConnected = await _apiService.checkServerConnectivity();
      if (!isConnected) {
        ErrorHandler.instance.logWarning(
          'Échec de connexion au serveur avec l\'URL par défaut, tentative de découverte automatique...',
          context: 'UnifiedAuthService.login',
        );

        // Essayer la découverte automatique du serveur
        final networkDiscovery = NetworkDiscoveryService();
        final discoveredServers = await networkDiscovery.testDevelopmentIPs();

        if (discoveredServers.isNotEmpty) {
          final serverIp = discoveredServers.first;
          final newBaseUrl = 'http://$serverIp:3005/api';
          _apiService.setBaseUrl(newBaseUrl);

          ErrorHandler.instance.logInfo(
            'Serveur découvert automatiquement: $serverIp',
            context: 'UnifiedAuthService.login',
          );

          // Vérifier à nouveau la connectivité
          isConnected = await _apiService.checkServerConnectivity();
        }

        if (!isConnected) {
          ErrorHandler.instance.logError(
            'Échec de connexion au serveur après découverte automatique.',
            context: 'UnifiedAuthService.login',
          );
          throw AppError(
            message:
                'Impossible de se connecter au serveur. Vérifiez que le serveur backend est démarré et que votre appareil est sur le même réseau.',
            type: ErrorType.network,
            context: 'UnifiedAuthService.login',
          );
        }
      }

      // Journaliser les informations de connexion pour le débogage
      ErrorHandler.instance.logInfo(
        'Tentative de connexion avec l\'URL: ${_apiService.getBaseUrl()}',
        context: 'UnifiedAuthService.login',
      );

      // Essayer plusieurs adresses IP si nécessaire
      Map<String, dynamic> response;
      try {
        // Appeler l'API de connexion
        response = await _apiService.post('auth/login', {
          'email': email,
          'password': password,
        });
      } catch (e) {
        // Si l'erreur est liée à un format HTML, essayer avec d'autres adresses IP
        if (e.toString().contains('HTML') ||
            e.toString().contains('<!DOCTYPE')) {
          ErrorHandler.instance.logWarning(
            'Réponse HTML détectée, tentative avec d\'autres adresses IP',
            context: 'UnifiedAuthService.login',
          );

          // Essayer directement avec l'adresse IP Wi-Fi
          _apiService.setBaseUrl('http://*************:3005/api');
          response = await _apiService.post('auth/login', {
            'email': email,
            'password': password,
          });
        } else {
          // Si ce n'est pas une erreur de format HTML, relancer l'erreur
          rethrow;
        }
      }

      // Extraire et sauvegarder le token
      if (response.containsKey('token')) {
        final token = response['token'];
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_tokenKey, token);
        _apiService.setAuthToken(token);

        ErrorHandler.instance.logInfo(
          'Token d\'authentification sauvegardé',
          context: 'UnifiedAuthService.login',
        );
      } else {
        ErrorHandler.instance.logWarning(
          'Aucun token d\'authentification reçu',
          context: 'UnifiedAuthService.login',
        );
      }

      // Extraire les données utilisateur
      dynamic userData;
      if (response.containsKey('data')) {
        userData = response['data'];
      } else if (response.containsKey('user')) {
        userData = response['user'];
      } else {
        // Créer un objet utilisateur à partir des données disponibles
        userData = Map<String, dynamic>.from(response);
        // Supprimer les champs qui ne sont pas des données utilisateur
        userData.remove('token');
        userData.remove('success');
        userData.remove('message');
        userData.remove('status');
      }

      // Créer l'objet utilisateur
      final user = _modelService.createUserFromMap(
        _modelService.standardizeMap(userData),
      );

      if (user != null) {
        await saveCurrentUser(user);

        // Journaliser le succès
        ErrorHandler.instance.logInfo(
          'Connexion réussie: $email',
          context: 'UnifiedAuthService',
        );

        return user;
      } else {
        throw AppError(
          message: 'Informations de connexion invalides',
          type: ErrorType.authentication,
          context: 'UnifiedAuthService.login',
        );
      }
    } on AppError catch (e) {
      ErrorHandler.instance.logError(e, context: 'UnifiedAuthService.login');
      rethrow;
    } catch (e) {
      ErrorHandler.instance.logError(e, context: 'UnifiedAuthService.login');
      throw AppError(
        message: 'Erreur lors de la connexion: ${e.toString()}',
        type: ErrorType.unknown,
        originalError: e,
        context: 'UnifiedAuthService.login',
      );
    }
  }

  /// Inscription
  Future<dynamic> register(Map<String, dynamic> userData) async {
    try {
      // Journaliser l'action
      ErrorHandler.instance.logInfo(
        'Tentative d\'inscription: ${userData['email']}',
        context: 'UnifiedAuthService',
      );

      // Vérifier la connectivité au serveur avec plus de détails
      final isConnected = await _apiService.checkServerConnectivity();
      if (!isConnected) {
        ErrorHandler.instance.logError(
          'Échec de connexion au serveur lors de l\'inscription. Vérifiez que le serveur est démarré et accessible.',
          context: 'UnifiedAuthService.register',
        );
        throw AppError(
          message:
              'Impossible de se connecter au serveur. Vérifiez que le serveur backend est démarré et que votre appareil est sur le même réseau.',
          type: ErrorType.network,
          context: 'UnifiedAuthService.register',
        );
      }

      // Journaliser les données d'inscription pour le débogage
      ErrorHandler.instance.logInfo(
        'Données d\'inscription: ${userData.toString()}',
        context: 'UnifiedAuthService.register',
      );

      // Journaliser l'URL utilisée pour l'inscription
      ErrorHandler.instance.logInfo(
        'Tentative d\'inscription avec l\'URL: ${_apiService.getBaseUrl()}',
        context: 'UnifiedAuthService.register',
      );

      // Essayer plusieurs adresses IP si nécessaire
      Map<String, dynamic> response;
      try {
        // Appeler l'API d'inscription
        response = await _apiService.post('auth/register', userData);
      } catch (e) {
        // Si l'erreur est liée à un format HTML, essayer avec d'autres adresses IP
        if (e.toString().contains('HTML') ||
            e.toString().contains('<!DOCTYPE')) {
          ErrorHandler.instance.logWarning(
            'Réponse HTML détectée lors de l\'inscription, tentative avec d\'autres adresses IP',
            context: 'UnifiedAuthService.register',
          );

          // Essayer directement avec l'adresse IP Wi-Fi
          _apiService.setBaseUrl('http://*************:3005/api');
          response = await _apiService.post('auth/register', userData);
        } else {
          // Si ce n'est pas une erreur de format HTML, relancer l'erreur
          rethrow;
        }
      }

      // Journaliser la réponse complète pour le débogage
      ErrorHandler.instance.logInfo(
        'Réponse d\'inscription: ${response.toString()}',
        context: 'UnifiedAuthService.register',
      );

      // Vérifier si la réponse contient un token
      if (!response.containsKey('token')) {
        ErrorHandler.instance.logWarning(
          'Réponse d\'inscription sans token: ${response.toString()}',
          context: 'UnifiedAuthService.register',
        );
        throw AppError(
          message:
              'Erreur lors de l\'inscription: token manquant dans la réponse',
          type: ErrorType.unknown,
          context: 'UnifiedAuthService.register',
        );
      }

      // Vérifier si la réponse contient des informations utilisateur
      Map<String, dynamic> userMap;
      if (response.containsKey('user')) {
        userMap = response['user'];
      } else if (response.containsKey('data')) {
        userMap = response['data'];
      } else {
        // Essayer d'extraire les données utilisateur de la réponse directement
        userMap = Map<String, dynamic>.from(response);
        // Supprimer les champs qui ne sont pas des données utilisateur
        userMap.remove('token');
        userMap.remove('success');
        userMap.remove('message');
      }

      if (userMap.isEmpty) {
        ErrorHandler.instance.logWarning(
          'Réponse d\'inscription sans données utilisateur: ${response.toString()}',
          context: 'UnifiedAuthService.register',
        );
        throw AppError(
          message:
              'Erreur lors de l\'inscription: données utilisateur manquantes',
          type: ErrorType.unknown,
          context: 'UnifiedAuthService.register',
        );
      }

      // Sauvegarder le token
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_tokenKey, response['token']);
      _apiService.setAuthToken(response['token']);

      // Créer l'objet utilisateur
      final user = _modelService.createUserFromMap(
        _modelService.standardizeMap(userMap),
      );
      await saveCurrentUser(user);

      // Journaliser le succès
      ErrorHandler.instance.logInfo(
        'Inscription réussie: ${userData['email']}',
        context: 'UnifiedAuthService',
      );

      return user;
    } on AppError catch (e) {
      ErrorHandler.instance.logError(e, context: 'UnifiedAuthService.register');
      rethrow;
    } catch (e) {
      ErrorHandler.instance.logError(e, context: 'UnifiedAuthService.register');
      throw AppError(
        message: 'Erreur lors de l\'inscription: ${e.toString()}',
        type: ErrorType.unknown,
        originalError: e,
        context: 'UnifiedAuthService.register',
      );
    }
  }

  /// Mettre à jour le profil utilisateur
  Future<dynamic> updateProfile(
    String userId,
    Map<String, dynamic> userData,
  ) async {
    try {
      // Déterminer le type d'utilisateur
      final prefs = await SharedPreferences.getInstance();
      final userType = prefs.getString(_userTypeKey) ?? 'client';

      String endpoint;
      switch (userType) {
        case 'pecheur':
          endpoint = 'pecheurs/$userId';
          break;
        case 'veterinaire':
          endpoint = 'veterinaires/$userId';
          break;
        case 'maryeur':
          endpoint = 'maryeurs/$userId';
          break;
        case 'admin':
          endpoint = 'admins/$userId';
          break;
        default:
          endpoint = 'clients/$userId';
      }

      final response = await _apiService.put(endpoint, userData);

      if (response.containsKey('data')) {
        final updatedUser = _modelService.createUserFromMap(
          _modelService.standardizeMap(response['data']),
        );
        await saveCurrentUser(updatedUser);
        return updatedUser;
      }

      return null;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedAuthService.updateProfile',
      );
      rethrow;
    }
  }

  /// Changer le mot de passe
  Future<bool> changePassword(
    String userId,
    String oldPassword,
    String newPassword,
  ) async {
    try {
      // Déterminer le type d'utilisateur
      final prefs = await SharedPreferences.getInstance();
      final userType = prefs.getString(_userTypeKey) ?? 'client';

      String endpoint;
      switch (userType) {
        case 'pecheur':
          endpoint = 'pecheurs/$userId/password';
          break;
        case 'veterinaire':
          endpoint = 'veterinaires/$userId/password';
          break;
        case 'maryeur':
          endpoint = 'maryeurs/$userId/password';
          break;
        case 'admin':
          endpoint = 'admins/$userId/password';
          break;
        default:
          endpoint = 'clients/$userId/password';
      }

      await _apiService.put(endpoint, {
        'oldPassword': oldPassword,
        'newPassword': newPassword,
      });

      return true;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedAuthService.changePassword',
      );
      return false;
    }
  }
}
