import 'package:flutter/material.dart';
import 'package:seatrace/models/lot.dart';
import 'package:seatrace/services/unified_lot_service.dart';
import 'package:seatrace/utils/animation_service.dart';
import 'package:seatrace/widgets/species_filter_widget.dart';
import 'package:intl/intl.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  final UnifiedLotService _lotService = UnifiedLotService();
  final AnimationService _animationService = AnimationService();

  List<Lot> _allLots = [];
  List<Lot> _filteredLots = [];
  bool _isLoading = true;
  String? _errorMessage;
  String _filterStatus = 'Tous';
  String? _selectedSpecies;
  String _sortBy = 'Date (récent)';

  final List<String> _statusFilters = [
    'Tous',
    'En attente',
    'Validé',
    'Refusé',
    'Vendu',
  ];

  @override
  void initState() {
    super.initState();
    _loadLots();
  }

  Future<void> _loadLots() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final allLots = await _lotService.getLotsByPecheur();

      setState(() {
        _allLots = allLots;
        _applyFilters();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur lors du chargement: $e';
        _isLoading = false;
      });
    }
  }

  void _applyFilters() {
    List<Lot> filteredLots =
        _allLots.where((lot) {
          // Filtre par statut
          bool statusMatch = true;
          if (_filterStatus != 'Tous') {
            switch (_filterStatus) {
              case 'En attente':
                statusMatch = !lot.test;
                break;
              case 'Validé':
                statusMatch = lot.test && lot.isValidated == true;
                break;
              case 'Refusé':
                statusMatch = lot.test && lot.isValidated == false;
                break;
              case 'Vendu':
                statusMatch = lot.vendu;
                break;
              default:
                statusMatch = true;
            }
          }

          // Filtre par espèce
          bool speciesMatch = true;
          if (_selectedSpecies != null && _selectedSpecies!.isNotEmpty) {
            speciesMatch =
                lot.especeNom.toLowerCase() == _selectedSpecies!.toLowerCase();
          }

          return statusMatch && speciesMatch;
        }).toList();

    // Appliquer le tri
    filteredLots.sort((a, b) {
      switch (_sortBy) {
        case 'Date (récent)':
          return (b.dateSoumission ?? DateTime.now()).compareTo(
            a.dateSoumission ?? DateTime.now(),
          );
        case 'Date (ancien)':
          return (a.dateSoumission ?? DateTime.now()).compareTo(
            b.dateSoumission ?? DateTime.now(),
          );
        case 'Espèce':
          return a.especeNom.compareTo(b.especeNom);
        default:
          return 0;
      }
    });

    setState(() {
      _filteredLots = filteredLots;
    });
  }

  List<String> get _availableSpecies {
    final Set<String> species = <String>{};
    for (final lot in _allLots) {
      if (lot.especeNom.isNotEmpty) {
        species.add(lot.especeNom);
      }
    }
    return species.toList()..sort();
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = const Color(0xFF1565C0);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Historique des Lots'),
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadLots,
            tooltip: 'Actualiser',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            tooltip: 'Trier',
            onSelected: (value) {
              setState(() {
                _sortBy = value;
                _applyFilters();
              });
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'Date (récent)',
                    child: Text('Date (récent)'),
                  ),
                  const PopupMenuItem(
                    value: 'Date (ancien)',
                    child: Text('Date (ancien)'),
                  ),
                  const PopupMenuItem(
                    value: 'Espèce',
                    child: Text('Espèce (A-Z)'),
                  ),
                ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Barre de filtres
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Filtres par statut et espèce
                Row(
                  children: [
                    // Filtre par statut
                    Expanded(
                      flex: 2,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: _filterStatus,
                            isExpanded: true,
                            hint: const Text('Statut'),
                            onChanged: (value) {
                              setState(() {
                                _filterStatus = value ?? 'Tous';
                                _applyFilters();
                              });
                            },
                            items:
                                _statusFilters.map((status) {
                                  return DropdownMenuItem<String>(
                                    value: status,
                                    child: Text(status),
                                  );
                                }).toList(),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Filtre par espèce
                    Expanded(
                      flex: 2,
                      child: SpeciesFilterWidget(
                        selectedSpecies: _selectedSpecies,
                        onSpeciesChanged: (species) {
                          setState(() {
                            _selectedSpecies = species;
                            _applyFilters();
                          });
                        },
                        availableSpecies: _availableSpecies,
                      ),
                    ),
                  ],
                ),

                // Chips des filtres actifs
                if (_selectedSpecies != null || _filterStatus != 'Tous') ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      if (_filterStatus != 'Tous')
                        Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: Chip(
                            label: Text('Statut: $_filterStatus'),
                            onDeleted: () {
                              setState(() {
                                _filterStatus = 'Tous';
                                _applyFilters();
                              });
                            },
                          ),
                        ),
                      if (_selectedSpecies != null)
                        SelectedSpeciesChip(
                          species: _selectedSpecies!,
                          onRemove: () {
                            setState(() {
                              _selectedSpecies = null;
                              _applyFilters();
                            });
                          },
                        ),
                    ],
                  ),
                ],
              ],
            ),
          ),

          // Statistiques rapides
          Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatChip('Total', _allLots.length, Colors.blue),
                _buildStatChip(
                  'En attente',
                  _allLots.where((l) => !l.test).length,
                  Colors.orange,
                ),
                _buildStatChip(
                  'Validés',
                  _allLots.where((l) => l.test && l.isValidated).length,
                  Colors.green,
                ),
                _buildStatChip(
                  'Vendus',
                  _allLots.where((l) => l.vendu).length,
                  Colors.purple,
                ),
              ],
            ),
          ),

          const Divider(height: 1),

          // Liste des lots
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _errorMessage != null
                    ? _buildErrorState()
                    : _filteredLots.isEmpty
                    ? _buildEmptyState()
                    : RefreshIndicator(
                      onRefresh: _loadLots,
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _filteredLots.length,
                        itemBuilder: (context, index) {
                          final lot = _filteredLots[index];
                          return _animationService.fadeIn(
                            _buildLotCard(lot),
                            duration: Duration(
                              milliseconds: 300 + (index * 50),
                            ),
                          );
                        },
                      ),
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatChip(String label, int count, Color color) {
    return Column(
      children: [
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
      ],
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
          const SizedBox(height: 16),
          Text(
            'Erreur de chargement',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage ?? 'Une erreur est survenue',
            style: TextStyle(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(onPressed: _loadLots, child: const Text('Réessayer')),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inbox_outlined, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Aucun lot trouvé',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Aucun lot ne correspond aux filtres sélectionnés',
            style: TextStyle(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLotCard(Lot lot) {
    final theme = Theme.of(context);
    final primaryColor = const Color(0xFF1565C0);

    // Déterminer le statut et la couleur
    String status;
    Color statusColor;
    if (lot.vendu) {
      status = 'Vendu';
      statusColor = Colors.green;
    } else if (lot.test && lot.isValidated) {
      status = 'Validé';
      statusColor = Colors.blue;
    } else if (lot.test && !lot.isValidated) {
      status = 'Refusé';
      statusColor = Colors.red;
    } else {
      status = 'En attente';
      statusColor = Colors.orange;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showLotDetails(lot),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: statusColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      status,
                      style: TextStyle(
                        color: statusColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    lot.identifiant,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          lot.especeNom,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: primaryColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${lot.poids} kg',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[700],
                          ),
                        ),
                        if (lot.vendu && lot.prixFinal != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            'Vendu: ${lot.prixFinal} DT',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: Colors.green[700],
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  if (lot.photo != null)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        lot.photo!,
                        width: 60,
                        height: 60,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.image_not_supported,
                              color: Colors.grey[400],
                            ),
                          );
                        },
                      ),
                    ),
                ],
              ),

              // Affichage du motif de refus si le lot est refusé
              if (lot.test &&
                  !lot.isValidated &&
                  (lot.motifRefus != null || lot.raisonRejet != null)) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.red.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.warning_amber_rounded,
                        size: 20,
                        color: Colors.red[700],
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Motif de refus:',
                              style: TextStyle(
                                color: Colors.red[700],
                                fontWeight: FontWeight.w600,
                                fontSize: 13,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              lot.motifRefus ??
                                  lot.raisonRejet ??
                                  'Non spécifié',
                              style: TextStyle(
                                color: Colors.red[600],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'Soumis le ${DateFormat('dd/MM/yyyy à HH:mm').format(lot.dateSoumission ?? DateTime.now())}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showLotDetails(Lot lot) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Détails du Lot ${lot.identifiant}'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildDetailRow('Espèce', lot.especeNom),
                  _buildDetailRow('Poids', '${lot.poids} kg'),
                  _buildDetailRow('Température', '${lot.temperature}°C'),
                  _buildDetailRow(
                    'Date soumission',
                    DateFormat(
                      'dd/MM/yyyy à HH:mm',
                    ).format(lot.dateSoumission ?? DateTime.now()),
                  ),
                  if (lot.dateTest != null)
                    _buildDetailRow(
                      'Date test',
                      DateFormat('dd/MM/yyyy à HH:mm').format(lot.dateTest!),
                    ),
                  _buildDetailRow(
                    'Statut',
                    lot.vendu
                        ? 'Vendu'
                        : lot.test
                        ? (lot.isValidated ? 'Validé' : 'Refusé')
                        : 'En attente',
                  ),
                  if (lot.prixFinal != null)
                    _buildDetailRow('Prix final', '${lot.prixFinal} DT'),
                  if ((lot.motifRefus != null && lot.motifRefus!.isNotEmpty) ||
                      (lot.raisonRejet != null && lot.raisonRejet!.isNotEmpty))
                    _buildDetailRow(
                      'Motif de refus',
                      lot.motifRefus ?? lot.raisonRejet ?? 'Non spécifié',
                    ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
