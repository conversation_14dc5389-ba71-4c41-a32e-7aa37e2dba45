import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:seatrace/models/pecheur.dart';
import 'package:seatrace/services/unified_api_service.dart';
import 'package:seatrace/services/unified_auth_service.dart';
import 'package:seatrace/utils/animation_service.dart';
import 'package:seatrace/utils/responsive_service.dart';
import 'package:seatrace/widgets/sea_widgets.dart';
import 'package:intl/intl.dart';

class FishDetailsScreen extends StatefulWidget {
  final File imageFile;
  final String especeNom;

  const FishDetailsScreen({
    super.key,
    required this.imageFile,
    required this.especeNom,
  });

  @override
  State<FishDetailsScreen> createState() => _FishDetailsScreenState();
}

class _FishDetailsScreenState extends State<FishDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _poidController = TextEditingController();
  final _temperatureController = TextEditingController();
  final _enginController = TextEditingController();
  final _zoneController = TextEditingController();
  final _emplacementController = TextEditingController();
  
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  // Liste des mareyeurs disponibles
  List<Map<String, dynamic>> _maryeurs = [];
  Map<String, dynamic>? _selectedMaryeur;

  // Liste des vétérinaires disponibles
  List<Map<String, dynamic>> _veterinaires = [];
  Map<String, dynamic>? _selectedVeterinaire;

  final _animationService = AnimationService();
  final _responsiveService = ResponsiveService();

  final List<String> _methodesDepeche = [
    'Filet',
    'Ligne',
    'Chalut',
    'Casier',
    'Palangre',
    'Autre',
  ];

  final List<String> _zonesDepeche = [
    'Méditerranée Nord',
    'Méditerranée Sud',
    'Atlantique Nord',
    'Atlantique Sud',
    'Manche',
    'Mer du Nord',
    'Autre',
  ];

  @override
  void initState() {
    super.initState();
    _enginController.text = 'Filet';
    _zoneController.text = 'Méditerranée Nord';
    _temperatureController.text = '4';

    // Charger la liste des mareyeurs et des vétérinaires
    _loadMaryeurs();
    _loadVeterinaires();
  }

  @override
  void dispose() {
    _poidController.dispose();
    _temperatureController.dispose();
    _enginController.dispose();
    _zoneController.dispose();
    _emplacementController.dispose();
    super.dispose();
  }

  // Charger la liste des mareyeurs disponibles
  Future<void> _loadMaryeurs() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final user = await UnifiedAuthService().getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non connecté');
      }

      final apiService = UnifiedApiService();
      final isConnected = await apiService.checkServerConnectivity();
      if (!isConnected) {
        throw Exception('Impossible de se connecter au serveur');
      }

      final maryeursData = await apiService.getAllMaryeurs();

      // Convertir les objets Maryeur en Map pour la compatibilité
      final maryeurs = <Map<String, dynamic>>[];

      for (final mar in maryeursData) {
        final mongoId = mar.id;
        final nom = mar.nom;
        final prenom = mar.prenom;
        final isValidated = mar.isValidated;

        if (mongoId == null || mongoId.isEmpty || nom.isEmpty || prenom.isEmpty) {
          continue;
        }

        final maryeurMap = {
          '_id': mongoId,
          'nom': nom,
          'prenom': prenom,
          'email': mar.email,
          'telephone': mar.telephone ?? '',
          'port': mar.port ?? '',
          'matricule': mar.matricule ?? '',
          'isValidated': isValidated,
        };

        maryeurs.add(maryeurMap);
      }

      String? errorMsg;
      if (maryeurs.isEmpty) {
        errorMsg = 'Aucun mareyeur validé disponible dans la base de données. '
            'Veuillez contacter l\'administrateur pour ajouter des mareyeurs.';
      }

      if (mounted) {
        setState(() {
          _maryeurs = maryeurs;
          _isLoading = false;

          if (_maryeurs.isNotEmpty) {
            _selectedMaryeur = _maryeurs[0];
          } else {
            _errorMessage = errorMsg;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _maryeurs = [];
          _selectedMaryeur = null;

          if (e.toString().contains('network') ||
              e.toString().contains('connexion') ||
              e.toString().contains('SocketException') ||
              e.toString().contains('timeout')) {
            _errorMessage = 'Problème de connexion au serveur. Vérifiez que le serveur backend est en cours d\'exécution et que votre connexion internet fonctionne.';
          } else {
            _errorMessage = 'Erreur lors du chargement des mareyeurs: ${e.toString()}. Veuillez réessayer ou contacter l\'administrateur.';
          }
        });
      }
    }
  }

  // Charger la liste des vétérinaires disponibles
  Future<void> _loadVeterinaires() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final user = await UnifiedAuthService().getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non connecté');
      }

      final apiService = UnifiedApiService();
      final isConnected = await apiService.checkServerConnectivity();
      if (!isConnected) {
        throw Exception('Impossible de se connecter au serveur');
      }

      final veterinairesData = await apiService.getAllVeterinaires();

      // Convertir les objets Veterinaire en Map pour la compatibilité
      final veterinaires = <Map<String, dynamic>>[];

      for (final vet in veterinairesData) {
        final mongoId = vet.id;
        final nom = vet.nom;
        final prenom = vet.prenom;
        final isValidated = vet.isValidated;

        if (mongoId == null || mongoId.isEmpty || nom.isEmpty || prenom.isEmpty) {
          continue;
        }

        final veterinaireMap = {
          '_id': mongoId,
          'nom': nom,
          'prenom': prenom,
          'email': vet.email,
          'telephone': vet.telephone ?? '',
          'port': vet.port ?? '',
          'matricule': vet.matricule ?? '',
          'isValidated': isValidated,
        };

        veterinaires.add(veterinaireMap);
      }

      String? errorMsg;
      if (veterinaires.isEmpty) {
        errorMsg = 'Aucun vétérinaire validé disponible dans la base de données. '
            'Veuillez contacter l\'administrateur pour ajouter des vétérinaires.';
      }

      if (mounted) {
        setState(() {
          _veterinaires = veterinaires;
          _isLoading = false;

          if (_veterinaires.isNotEmpty) {
            _selectedVeterinaire = _veterinaires[0];
          } else {
            _errorMessage = errorMsg;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _veterinaires = [];
          _selectedVeterinaire = null;

          if (e.toString().contains('network') ||
              e.toString().contains('connexion') ||
              e.toString().contains('SocketException') ||
              e.toString().contains('timeout')) {
            _errorMessage = 'Problème de connexion au serveur. Vérifiez que le serveur backend est en cours d\'exécution et que votre connexion internet fonctionne.';
          } else {
            _errorMessage = 'Erreur lors du chargement des vétérinaires: ${e.toString()}. Veuillez réessayer ou contacter l\'administrateur.';
          }
        });
      }
    }
  }

  // Méthode utilitaire pour convertir les IDs MongoDB en string
  String _convertIdToString(dynamic id) {
    if (id == null) return '';
    
    if (id is String) {
      return id;
    } else if (id is Map && id.containsKey('\$oid')) {
      return id['\$oid'].toString();
    } else {
      return id.toString();
    }
  }

  // Méthode pour rafraîchir toutes les données
  Future<void> _refreshAllData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    await Future.wait([
      _loadMaryeurs(),
      _loadVeterinaires(),
    ]);

    setState(() {
      _isLoading = false;
    });
  }
