// Test pour vérifier les statistiques des vétérinaires
const axios = require('axios');

const BASE_URL = 'http://localhost:3005/api';

async function testVeterinaireStats() {
  console.log('🚀 Test des statistiques vétérinaire\n');

  try {
    // 1. Connexion du vétérinaire
    console.log('🔐 Connexion du vétérinaire...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'Insaf12*'
    });

    const token = loginResponse.data.token;
    const veterinaire = loginResponse.data.data || loginResponse.data;

    console.log('✅ Connexion réussie');
    console.log(`Vétérinaire: ${veterinaire.prenom} ${veterinaire.nom}`);
    console.log(`ID: ${veterinaire.id || veterinaire._id}`);

    // 2. Test de l'endpoint /lots/pending
    console.log('\n📦 Test de l\'endpoint /lots/pending...');
    const pendingResponse = await axios.get(`${BASE_URL}/lots/pending`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    console.log(`✅ Réponse de l'API /lots/pending:`);
    console.log(`- Status: ${pendingResponse.status}`);
    console.log(`- Success: ${pendingResponse.data.success}`);
    console.log(`- Message: ${pendingResponse.data.message}`);

    if (pendingResponse.data.data) {
      const lots = Array.isArray(pendingResponse.data.data) ? pendingResponse.data.data : [pendingResponse.data.data];
      console.log(`- Nombre de lots: ${lots.length}`);

      if (lots.length > 0) {
        console.log('\n📋 Détails des lots en attente:');
        lots.forEach((lot, index) => {
          console.log(`  Lot ${index + 1}:`);
          console.log(`    - ID: ${lot.id || lot._id}`);
          console.log(`    - Identifiant: ${lot.identifiant}`);
          console.log(`    - Espèce: ${lot.especeNom}`);
          console.log(`    - Test: ${lot.test}`);
          console.log(`    - IsValidated: ${lot.isValidated}`);
          console.log(`    - User: ${lot.user ? 'Présent' : 'Absent'}`);
          console.log(`    - Photo: ${lot.photo ? 'Présente' : 'Absente'}`);
        });
      }
    } else {
      console.log('- Aucune donnée retournée');
    }

    // 3. Test de l'endpoint de statistiques (si disponible)
    console.log('\n📊 Test de l\'endpoint de statistiques...');
    try {
      const statsResponse = await axios.get(`${BASE_URL}/stats/veterinaire/${veterinaire.id || veterinaire._id}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      console.log(`✅ Réponse de l'API /stats/veterinaire:`);
      console.log(`- Status: ${statsResponse.status}`);
      console.log(`- Success: ${statsResponse.data.success}`);
      console.log(`- Données:`, statsResponse.data.data);
    } catch (statsError) {
      console.log(`❌ Erreur stats: ${statsError.response?.status} - ${statsError.response?.data?.error || statsError.message}`);
    }

    console.log('\n🎉 Test terminé avec succès!');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error.response?.status, '-', error.response?.data?.error || error.message);
    if (error.response?.data) {
      console.error('Données d\'erreur:', error.response.data);
    }
  }
}

testVeterinaireStats();
