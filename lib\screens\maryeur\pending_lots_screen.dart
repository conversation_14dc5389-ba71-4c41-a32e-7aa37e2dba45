import 'package:flutter/material.dart';
import 'package:seatrace/services/unified_api_service.dart';
import 'package:seatrace/services/unified_auth_service.dart';
import 'package:seatrace/services/unified_lot_service.dart';
import 'package:seatrace/models/maryeur.dart';
import 'package:seatrace/models/lot.dart';
import 'package:seatrace/utils/error_handler.dart';
import 'package:intl/intl.dart';

class PendingLotsMaryeurScreen extends StatefulWidget {
  const PendingLotsMaryeurScreen({super.key});

  @override
  State<PendingLotsMaryeurScreen> createState() =>
      _PendingLotsMaryeurScreenState();
}

class _PendingLotsMaryeurScreenState extends State<PendingLotsMaryeurScreen> {
  List<Lot> _pendingLots = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadPendingLots();
  }

  Future<void> _loadPendingLots() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Vérifier l'utilisateur avec le service unifié
      final user = await UnifiedAuthService().getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non autorisé');
      }

      // Vérifier que l'utilisateur est bien un maryeur
      if (user is! Maryeur) {
        throw Exception('L\'utilisateur n\'est pas un maryeur');
      }

      // Ajouter un log pour le débogage
      ErrorHandler.instance.logInfo(
        'Chargement des lots en attente de prix pour l\'utilisateur: ${user.id}',
        context: 'PendingLotsMaryeurScreen',
      );

      // Log pour le débogage
      ErrorHandler.instance.logInfo(
        'Utilisateur authentifié: ${user.id}',
        context: 'PendingLotsMaryeurScreen',
      );

      try {
        // Utiliser le service unifié pour récupérer les lots en attente de prix
        _pendingLots = await UnifiedLotService().getPendingPriceLots();

        ErrorHandler.instance.logInfo(
          'Lots en attente de prix chargés: ${_pendingLots.length}',
          context: 'PendingLotsMaryeurScreen',
        );
      } catch (e) {
        ErrorHandler.instance.logError(
          'Erreur API lors de la récupération des lots: $e',
          context: 'PendingLotsMaryeurScreen',
        );

        // Si c'est une erreur 404, on considère qu'il n'y a pas de lots en attente
        if (e.toString().contains('notFound') || e.toString().contains('404')) {
          _pendingLots = [];
          setState(() {
            _isLoading = false;
          });
          return;
        }
        throw Exception('Erreur lors de la récupération des lots: $e');
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      ErrorHandler.instance.logError(
        'Erreur générale: $e',
        context: 'PendingLotsMaryeurScreen',
      );
      setState(() {
        _errorMessage = 'Erreur lors du chargement: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  // Mise à jour pour définir uniquement le prix minimal
  Future<void> _setMinPrice(String lotId, String minPrice) async {
    try {
      final user = await UnifiedAuthService().getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non autorisé');
      }

      // Vérifier que l'utilisateur est bien un maryeur
      if (user is! Maryeur) {
        throw Exception('L\'utilisateur n\'est pas un maryeur');
      }

      // Log l'action pour le débogage
      ErrorHandler.instance.logInfo(
        'Définition du prix pour le lot $lotId: $minPrice TND',
        context: 'PendingLotsMaryeurScreen',
      );

      // Convertir le prix en nombre
      final double prixMinimal = double.tryParse(minPrice) ?? 0.0;
      if (prixMinimal <= 0) {
        throw Exception('Le prix doit être supérieur à 0');
      }

      // Utiliser le service unifié pour définir le prix du lot
      final success = await UnifiedLotService().setPriceLot(lotId, prixMinimal);

      if (success) {
        // Actualiser la liste
        _loadPendingLots();

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Prix minimal défini avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        throw Exception('Échec de la définition du prix');
      }
    } catch (e) {
      // Log l'erreur pour le débogage
      ErrorHandler.instance.logError(
        'Erreur lors de la définition du prix: $e',
        context: 'PendingLotsMaryeurScreen',
      );

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showSetPriceDialog(BuildContext context, Lot lot) {
    final minPriceController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Définir le prix minimal - ${lot.especeNom}'),
            content: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: minPriceController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'Prix minimal (TND)',
                      prefixIcon: Icon(Icons.price_change),
                      helperText: 'Le prix de départ de l\'enchère',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Veuillez entrer un prix minimal';
                      }
                      if (double.tryParse(value) == null) {
                        return 'Veuillez entrer un nombre valide';
                      }
                      if (double.parse(value) <= 0) {
                        return 'Le prix doit être supérieur à 0';
                      }
                      return null;
                    },
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () {
                  if (formKey.currentState!.validate()) {
                    Navigator.of(context).pop();
                    _setMinPrice(lot.id, minPriceController.text);
                  }
                },
                child: const Text('Confirmer'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Lots en attente de prix'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPendingLots,
            tooltip: 'Actualiser',
          ),
        ],
      ),
      body: SafeArea(
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Theme.of(context).colorScheme.error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.error,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _loadPendingLots,
                        child: const Text('Réessayer'),
                      ),
                    ],
                  ),
                )
                : _pendingLots.isEmpty
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.check_circle_outline,
                        size: 64,
                        color: Colors.green[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Aucun lot en attente',
                        style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Tous les lots ont des prix définis',
                        style: TextStyle(color: Colors.grey[500]),
                      ),
                    ],
                  ),
                )
                : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _pendingLots.length,
                  itemBuilder: (context, index) {
                    final lot = _pendingLots[index];
                    return _buildLotCard(context, lot);
                  },
                ),
      ),
    );
  }

  Widget _buildLotCard(BuildContext context, Lot lot) {
    final espece = lot.especeNom;
    final date =
        lot.dateTest != null
            ? DateFormat('dd/MM/yyyy').format(lot.dateTest!)
            : 'Date inconnue';
    final photoPath = lot.photo;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image and basic info
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                ),
                child:
                    photoPath != null && photoPath.isNotEmpty
                        ? Image.network(
                          UnifiedApiService().getImageUrl(photoPath),
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 120,
                              height: 120,
                              color: Colors.grey[300],
                              child: Icon(
                                Icons.image_not_supported,
                                size: 40,
                                color: Colors.grey[500],
                              ),
                            );
                          },
                        )
                        : Container(
                          width: 120,
                          height: 120,
                          color: Colors.grey[300],
                          child: Icon(
                            Icons.image_not_supported,
                            size: 40,
                            color: Colors.grey[500],
                          ),
                        ),
              ),

              // Info
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        espece,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Date: $date',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Quantité: ${lot.quantite} | Poids: ${lot.poids} kg',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Température: ${lot.temperature} °C',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          // Divider
          const Divider(),

          // Actions
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: TextButton.icon(
                    onPressed: () {
                      // View details action
                      _showLotDetails(context, lot);
                    },
                    icon: const Icon(Icons.visibility),
                    label: const Text('Détails'),
                  ),
                ),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showSetPriceDialog(context, lot),
                    icon: const Icon(Icons.price_change),
                    label: const Text('Définir prix'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showLotDetails(BuildContext context, Lot lot) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Détails - ${lot.especeNom}'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (lot.photo != null && lot.photo!.isNotEmpty) ...[
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        UnifiedApiService().getImageUrl(lot.photo!),
                        width: double.infinity,
                        height: 200,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: double.infinity,
                            height: 200,
                            color: Colors.grey[300],
                            child: Icon(
                              Icons.image_not_supported,
                              size: 50,
                              color: Colors.grey[500],
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                  _buildDetailItem('Identifiant', lot.identifiant),
                  _buildDetailItem('Espèce', lot.especeNom),
                  _buildDetailItem('Quantité', lot.quantite.toString()),
                  _buildDetailItem('Poids', '${lot.poids} kg'),
                  _buildDetailItem('Température', '${lot.temperature} °C'),
                  _buildDetailItem(
                    'Date de soumission',
                    lot.dateSoumission != null
                        ? DateFormat(
                          'dd/MM/yyyy HH:mm',
                        ).format(lot.dateSoumission!)
                        : 'N/A',
                  ),

                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).pop();
                      _showSetPriceDialog(context, lot);
                    },
                    icon: const Icon(Icons.price_change),
                    label: const Text('Définir le prix minimal'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('$label: ', style: const TextStyle(fontWeight: FontWeight.bold)),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
