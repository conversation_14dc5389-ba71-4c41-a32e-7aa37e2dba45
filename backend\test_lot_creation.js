const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3005/api';

// Données de test pour un pêcheur
const testPecheur = {
  email: 'z<PERSON><EMAIL>',
  password: '<PERSON><PERSON><PERSON>er1*'
};

// Fonction pour se connecter et obtenir un token
async function login() {
  try {
    console.log('🔐 Connexion du pêcheur...');
    const response = await axios.post(`${BASE_URL}/auth/login`, testPecheur);

    if (response.data && response.data.token) {
      console.log('✅ Connexion réussie');
      console.log('Token:', response.data.token.substring(0, 20) + '...');
      console.log('Utilisateur:', response.data.user.prenom, response.data.user.nom);
      console.log('Rôle:', response.data.user.roles);
      return {
        token: response.data.token,
        user: response.data.user
      };
    } else {
      throw new Error('Pas de token dans la réponse');
    }
  } catch (error) {
    console.error('❌ Erreur de connexion:', error.response?.data || error.message);
    throw error;
  }
}

// Fonction pour récupérer les vétérinaires
async function getVeterinaires(token) {
  try {
    console.log('\n🏥 Récupération des vétérinaires...');
    const response = await axios.get(`${BASE_URL}/veterinaires`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    console.log('Réponse complète vétérinaires:', JSON.stringify(response.data, null, 2));

    // Vérifier la structure de la réponse
    const veterinaires = response.data.data?.data || response.data.data || response.data || [];
    console.log('✅ Vétérinaires récupérés:', veterinaires.length);

    // Convertir les IDs buffer en string
    const processedVeterinaires = veterinaires.map(vet => {
      let id = vet._id;
      if (vet.id && vet.id.buffer) {
        // Convertir le buffer en array puis en Buffer
        const bufferArray = Object.values(vet.id.buffer);
        id = Buffer.from(bufferArray).toString('hex');
      }
      return {
        ...vet,
        _id: id
      };
    });

    return processedVeterinaires;
  } catch (error) {
    console.error('❌ Erreur récupération vétérinaires:', error.response?.data || error.message);
    return [];
  }
}

// Fonction pour récupérer les mareyeurs
async function getMareyeurs(token) {
  try {
    console.log('\n🐟 Récupération des mareyeurs...');
    const response = await axios.get(`${BASE_URL}/maryeurs`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    console.log('Réponse complète mareyeurs:', JSON.stringify(response.data, null, 2));

    // Vérifier la structure de la réponse
    const mareyeurs = response.data.data?.data || response.data.data || response.data || [];
    console.log('✅ Mareyeurs récupérés:', mareyeurs.length);

    // Convertir les IDs buffer en string
    const processedMareyeurs = mareyeurs.map(mar => {
      let id = mar._id;
      if (mar.id && mar.id.buffer) {
        // Convertir le buffer en array puis en Buffer
        const bufferArray = Object.values(mar.id.buffer);
        id = Buffer.from(bufferArray).toString('hex');
      }
      return {
        ...mar,
        _id: id
      };
    });

    return processedMareyeurs;
  } catch (error) {
    console.error('❌ Erreur récupération mareyeurs:', error.response?.data || error.message);
    return [];
  }
}

// Fonction pour créer un lot
async function createLot(token, user, veterinaire, mareyeur) {
  try {
    console.log('\n📦 Création du lot...');

    const lotData = {
      identifiant: `LOT-TEST-${Date.now()}`,
      nom: 'Lot de test Rouget',
      photo: 'test-image.jpg',
      poids: 2.5,
      temperature: 4,
      quantite: 1,
      especeNom: 'Rouget',
      engin: 'Filet',
      zone: 'Zone de test',
      lieu: 'Sfax',
      pecheur: user._id,
      user: user._id,
      veterinaire: veterinaire._id,
      maryeur: mareyeur._id,
      status: false,
      test: false,
      vendu: false,
      isValidated: false
    };

    console.log('Données du lot à envoyer:');
    console.log(JSON.stringify(lotData, null, 2));

    const response = await axios.post(`${BASE_URL}/lots`, lotData, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Lot créé avec succès!');
    console.log('Réponse du serveur:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('❌ Erreur création lot:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('Détails de l\'erreur:', JSON.stringify(error.response.data, null, 2));
    }
    throw error;
  }
}

// Fonction principale
async function main() {
  try {
    console.log('🚀 Test de création de lot\n');

    // 1. Se connecter
    const { token, user } = await login();

    // 2. Récupérer les vétérinaires
    const veterinaires = await getVeterinaires(token);
    if (veterinaires.length === 0) {
      throw new Error('Aucun vétérinaire trouvé');
    }

    // 3. Récupérer les mareyeurs
    const mareyeurs = await getMareyeurs(token);
    if (mareyeurs.length === 0) {
      throw new Error('Aucun mareyeur trouvé');
    }

    // 4. Sélectionner le premier vétérinaire et mareyeur
    const veterinaire = veterinaires[0];
    const mareyeur = mareyeurs[0];

    console.log('\n📋 Sélection:');
    console.log('Vétérinaire:', veterinaire.prenom, veterinaire.nom, '(ID:', veterinaire._id, ')');
    console.log('Mareyeur:', mareyeur.prenom, mareyeur.nom, '(ID:', mareyeur._id, ')');

    // 5. Créer le lot
    await createLot(token, user, veterinaire, mareyeur);

    console.log('\n🎉 Test terminé avec succès!');

  } catch (error) {
    console.error('\n💥 Test échoué:', error.message);
    process.exit(1);
  }
}

// Exécuter le test
main();
