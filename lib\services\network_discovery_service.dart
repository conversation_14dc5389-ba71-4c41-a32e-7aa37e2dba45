import 'dart:async';
import 'dart:io' show Platform;
import 'package:network_info_plus/network_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../utils/error_handler.dart';
import 'unified_api_service.dart';
import 'service_config.dart';

/// Service pour la découverte automatique du serveur sur le réseau local
class NetworkDiscoveryService {
  /// Instance singleton
  static final NetworkDiscoveryService _instance =
      NetworkDiscoveryService._internal();

  /// Constructeur factory pour accéder à l'instance singleton
  factory NetworkDiscoveryService() => _instance;

  /// Constructeur privé pour l'initialisation
  NetworkDiscoveryService._internal();

  /// Port du serveur
  final int _serverPort = 3005;

  /// Endpoint pour vérifier si un serveur est valide
  final String _healthEndpoint = '/api/health';

  /// Client HTTP pour les requêtes
  final http.Client _httpClient = http.Client();

  /// Résultat de la dernière découverte
  List<String> _discoveredServers = [];

  /// Getter pour les serveurs découverts
  List<String> get discoveredServers => _discoveredServers;

  /// Tester rapidement les adresses IP de développement
  Future<List<String>> testDevelopmentIPs({
    Function(String)? onProgress,
  }) async {
    try {
      if (onProgress != null) {
        onProgress('Test des adresses IP de développement...');
      }

      // Utiliser les adresses IP de développement définies dans ServiceConfig
      final developmentIPs = ServiceConfig.developmentIPs;

      ErrorHandler.instance.logInfo(
        'Test des adresses IP de développement: ${developmentIPs.join(', ')}',
        context: 'NetworkDiscoveryService.testDevelopmentIPs',
      );

      final validServers = await _verifyServers(
        developmentIPs,
        onProgress: onProgress,
      );

      if (validServers.isNotEmpty) {
        _discoveredServers = validServers;
        await _saveDiscoveredServers(validServers);

        ErrorHandler.instance.logInfo(
          'Serveurs de développement trouvés: ${validServers.join(', ')}',
          context: 'NetworkDiscoveryService.testDevelopmentIPs',
        );
      }

      return validServers;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'NetworkDiscoveryService.testDevelopmentIPs',
      );
      return [];
    }
  }

  /// Découvrir les serveurs disponibles sur le réseau local
  /// Retourne une liste d'adresses IP des serveurs trouvés
  Future<List<String>> discoverServers({
    Function(String)? onProgress,
    bool saveDiscovered = true,
  }) async {
    try {
      _discoveredServers = [];

      // D'abord, tester les adresses IP de développement
      if (onProgress != null) {
        onProgress('Test des adresses IP de développement...');
      }

      final developmentServers = await testDevelopmentIPs(
        onProgress: onProgress,
      );
      if (developmentServers.isNotEmpty) {
        return developmentServers;
      }

      // Vérifier si nous sommes sur un émulateur Android
      bool isEmulator = false;
      try {
        if (Platform.isAndroid) {
          isEmulator =
              true; // Considérer tous les appareils Android comme des émulateurs pour le test
          ErrorHandler.instance.logInfo(
            'Appareil Android détecté, traité comme émulateur pour le test',
            context: 'NetworkDiscoveryService.discoverServers',
          );
        }
      } catch (e) {
        // Ignorer les erreurs
      }

      // Si nous sommes sur un émulateur, utiliser directement ********
      if (isEmulator) {
        if (onProgress != null) {
          onProgress('Détection de l\'émulateur Android...');
          onProgress(
            'Utilisation de l\'adresse spéciale pour l\'émulateur: ********',
          );
        }

        // Vérifier si le serveur est accessible
        final isServerAccessible = await _verifyServers([
          '********',
        ], onProgress: onProgress);

        if (isServerAccessible.isNotEmpty) {
          _discoveredServers = isServerAccessible;

          // Sauvegarder le serveur découvert
          if (saveDiscovered) {
            await _saveDiscoveredServers(isServerAccessible);
          }

          return isServerAccessible;
        }
      }

      final info = NetworkInfo();

      // Obtenir l'adresse IP locale
      final String? ip = await info.getWifiIP();

      if (ip == null) {
        ErrorHandler.instance.logWarning(
          'Impossible d\'obtenir l\'adresse IP Wi-Fi',
          context: 'NetworkDiscoveryService.discoverServers',
        );
        return [];
      }

      // Extraire le préfixe du réseau (ex: 192.168.1)
      final subnet = ip.substring(0, ip.lastIndexOf('.'));

      ErrorHandler.instance.logInfo(
        'Découverte des serveurs sur le sous-réseau: $subnet.*',
        context: 'NetworkDiscoveryService.discoverServers',
      );

      if (onProgress != null) {
        onProgress('Scan du réseau $subnet.* en cours...');
      }

      // Liste des adresses IP potentielles à scanner
      final List<String> potentialServers = [];

      // Scanner les 255 adresses IP possibles du sous-réseau
      for (int i = 1; i <= 255; i++) {
        final ipToCheck = '$subnet.$i';

        // Ne pas scanner sa propre adresse IP
        if (ipToCheck == ip) continue;

        potentialServers.add(ipToCheck);

        // Mettre à jour le statut tous les 25 IPs
        if (i % 25 == 0 && onProgress != null) {
          onProgress('Scan en cours: $i/255 adresses vérifiées...');
        }
      }

      if (onProgress != null) {
        onProgress('Vérification de ${potentialServers.length} adresses IP...');
      }

      // Vérifier chaque serveur potentiel
      final validServers = await _verifyServers(
        potentialServers,
        onProgress: onProgress,
      );

      _discoveredServers = validServers;

      // Sauvegarder les serveurs découverts si demandé
      if (saveDiscovered && validServers.isNotEmpty) {
        await _saveDiscoveredServers(validServers);
      }

      return validServers;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'NetworkDiscoveryService.discoverServers',
      );
      return [];
    }
  }

  /// Vérifier si les serveurs potentiels sont des serveurs SeaTrace valides
  Future<List<String>> _verifyServers(
    List<String> potentialServers, {
    Function(String)? onProgress,
  }) async {
    final List<String> validServers = [];

    for (final String ip in potentialServers) {
      try {
        if (onProgress != null) {
          onProgress('Vérification du serveur: $ip');
        }

        final url = 'http://$ip:$_serverPort$_healthEndpoint';

        ErrorHandler.instance.logInfo(
          'Vérification du serveur: $url',
          context: 'NetworkDiscoveryService._verifyServers',
        );

        final response = await _httpClient
            .get(Uri.parse(url))
            .timeout(
              Duration(seconds: 5),
            ); // Augmenter le timeout pour l'émulateur

        if (response.statusCode >= 200 && response.statusCode < 300) {
          // Vérifier si la réponse contient des informations spécifiques à SeaTrace
          if (response.body.contains('SeaTrace') ||
              response.body.contains('status') ||
              response.body.contains('success')) {
            validServers.add(ip);

            ErrorHandler.instance.logInfo(
              'Serveur SeaTrace valide trouvé: $ip',
              context: 'NetworkDiscoveryService._verifyServers',
            );

            if (onProgress != null) {
              onProgress('✅ Serveur SeaTrace valide: $ip');
            }
          }
        }
      } catch (e) {
        // Ignorer les erreurs, ce n'est probablement pas un serveur valide
        ErrorHandler.instance.logInfo(
          'Serveur non valide: $ip - ${e.toString()}',
          context: 'NetworkDiscoveryService._verifyServers',
        );
      }
    }

    return validServers;
  }

  /// Sauvegarder les serveurs découverts dans les préférences
  Future<void> _saveDiscoveredServers(List<String> servers) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('discovered_servers', servers);

      // Sauvegarder le premier serveur comme serveur actif
      if (servers.isNotEmpty) {
        await prefs.setString('working_server_ip', servers.first);
      }

      ErrorHandler.instance.logInfo(
        'Serveurs découverts sauvegardés: ${servers.join(', ')}',
        context: 'NetworkDiscoveryService._saveDiscoveredServers',
      );
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'NetworkDiscoveryService._saveDiscoveredServers',
      );
    }
  }

  /// Charger les serveurs découverts depuis les préférences
  Future<List<String>> loadDiscoveredServers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final servers = prefs.getStringList('discovered_servers') ?? [];
      _discoveredServers = servers;
      return servers;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'NetworkDiscoveryService.loadDiscoveredServers',
      );
      return [];
    }
  }

  /// Connecter l'application au serveur spécifié
  Future<bool> connectToServer(String ip) async {
    try {
      final apiService = UnifiedApiService();

      // Si nous sommes sur un émulateur Android et que l'IP est locale, utiliser ********
      bool useEmulatorIp = false;
      try {
        if (Platform.isAndroid &&
            (ip == '127.0.0.1' ||
                ip == 'localhost' ||
                ip.startsWith('192.168.') ||
                ip.startsWith('10.0.'))) {
          useEmulatorIp = true;
          ErrorHandler.instance.logInfo(
            'Détection d\'une adresse locale sur émulateur Android, utilisation de ********',
            context: 'NetworkDiscoveryService.connectToServer',
          );
        }
      } catch (e) {
        // Ignorer les erreurs
      }

      final actualIp = useEmulatorIp ? '********' : ip;
      final baseUrl = 'http://$actualIp:$_serverPort';

      // Définir l'URL de base
      apiService.setBaseUrl('$baseUrl/api');

      // Vérifier la connectivité
      final isConnected = await apiService.checkServerConnectivity();

      if (isConnected) {
        // Sauvegarder l'adresse IP qui fonctionne
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('working_server_ip', ip);

        ErrorHandler.instance.logInfo(
          'Connexion réussie au serveur: $ip',
          context: 'NetworkDiscoveryService.connectToServer',
        );
      }

      return isConnected;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'NetworkDiscoveryService.connectToServer',
      );
      return false;
    }
  }
}
