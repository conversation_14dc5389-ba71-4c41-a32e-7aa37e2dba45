import 'package:flutter/material.dart';
import 'package:seatrace/models/lot.dart';
import 'package:seatrace/services/unified_lot_service.dart';
import 'package:seatrace/utils/error_handler.dart';
import 'package:seatrace/widgets/custom_button.dart';
import 'package:seatrace/widgets/custom_text_field.dart';

/// Dialogue pour prolonger la durée d'une enchère
class ExtendAuctionDialog extends StatefulWidget {
  final Lot lot;
  final Function onSuccess;

  const ExtendAuctionDialog({
    super.key,
    required this.lot,
    required this.onSuccess,
  });

  @override
  State<ExtendAuctionDialog> createState() => _ExtendAuctionDialogState();
}

class _ExtendAuctionDialogState extends State<ExtendAuctionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _minutesController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _minutesController.dispose();
    super.dispose();
  }

  /// Prolonge la durée de l'enchère
  Future<void> _extendAuction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final minutes = int.parse(_minutesController.text);
      final result = await UnifiedLotService().extendAuction(
        widget.lot.id,
        minutes,
      );

      if (result) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Enchère prolongée de $minutes minute(s)'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
          widget.onSuccess();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Erreur lors de la prolongation de l\'enchère'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'ExtendAuctionDialog._extendAuction',
      );
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Prolonger l\'enchère'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Lot: ${widget.lot.identifiant}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: _minutesController,
              labelText: 'Minutes supplémentaires',
              hintText: 'Entrez le nombre de minutes à ajouter',
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Veuillez entrer un nombre de minutes';
                }
                final minutes = int.tryParse(value);
                if (minutes == null) {
                  return 'Veuillez entrer un nombre valide';
                }
                if (minutes <= 0) {
                  return 'Le nombre de minutes doit être positif';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                CustomButton(
                  text: 'Annuler',
                  onPressed: () => Navigator.of(context).pop(),
                  color: Colors.grey,
                ),
                CustomButton(
                  text: 'Prolonger',
                  onPressed: _isLoading ? null : _extendAuction,
                  isLoading: _isLoading,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
