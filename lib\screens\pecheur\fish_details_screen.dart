import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:seatrace/models/pecheur.dart';
import 'package:seatrace/services/unified_api_service.dart';
import 'package:seatrace/services/unified_auth_service.dart';
import 'package:seatrace/utils/animation_service.dart';
import 'package:seatrace/utils/responsive_service.dart';
import 'package:seatrace/widgets/sea_widgets.dart';
import 'package:intl/intl.dart';

class FishDetailsScreen extends StatefulWidget {
  final File imageFile;
  final String especeNom;

  const FishDetailsScreen({
    super.key,
    required this.imageFile,
    required this.especeNom,
  });

  @override
  State<FishDetailsScreen> createState() => _FishDetailsScreenState();
}

class _FishDetailsScreenState extends State<FishDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _poidController = TextEditingController();
  final _temperatureController = TextEditingController();
  final _enginController = TextEditingController();
  final _zoneController = TextEditingController();
  final _emplacementController = TextEditingController();

  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  // Liste des mareyeurs disponibles
  List<Map<String, dynamic>> _maryeurs = [];
  Map<String, dynamic>? _selectedMaryeur;

  // Liste des vétérinaires disponibles
  List<Map<String, dynamic>> _veterinaires = [];
  Map<String, dynamic>? _selectedVeterinaire;

  final _animationService = AnimationService();
  final _responsiveService = ResponsiveService();

  final List<String> _methodesDepeche = [
    'Filet',
    'Ligne',
    'Chalut',
    'Casier',
    'Palangre',
    'Autre',
  ];

  final List<String> _zonesDepeche = [
    'Méditerranée Nord',
    'Méditerranée Sud',
    'Atlantique Nord',
    'Atlantique Sud',
    'Manche',
    'Mer du Nord',
    'Autre',
  ];

  @override
  void initState() {
    super.initState();
    _enginController.text = 'Filet';
    _zoneController.text = 'Méditerranée Nord';
    _temperatureController.text = '4';

    // Charger la liste des mareyeurs et des vétérinaires
    _loadMaryeurs();
    _loadVeterinaires();
  }

  @override
  void dispose() {
    _poidController.dispose();
    _temperatureController.dispose();
    _enginController.dispose();
    _zoneController.dispose();
    _emplacementController.dispose();
    super.dispose();
  }

  // Charger la liste des mareyeurs disponibles
  Future<void> _loadMaryeurs() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final user = await UnifiedAuthService().getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non connecté');
      }

      final apiService = UnifiedApiService();
      final isConnected = await apiService.checkServerConnectivity();
      if (!isConnected) {
        throw Exception('Impossible de se connecter au serveur');
      }

      final maryeursData = await apiService.getAllMaryeurs();

      // Convertir les objets Maryeur en Map pour la compatibilité
      final maryeurs = <Map<String, dynamic>>[];

      for (final mar in maryeursData) {
        final mongoId = mar.id;
        final nom = mar.nom;
        final prenom = mar.prenom;
        final isValidated = mar.isValidated;

        if (mongoId == null ||
            mongoId.isEmpty ||
            nom.isEmpty ||
            prenom.isEmpty) {
          continue;
        }

        final maryeurMap = {
          '_id': mongoId,
          'nom': nom,
          'prenom': prenom,
          'email': mar.email,
          'telephone': mar.telephone ?? '',
          'port': mar.port ?? '',
          'matricule': mar.matricule ?? '',
          'isValidated': isValidated,
        };

        maryeurs.add(maryeurMap);
      }

      String? errorMsg;
      if (maryeurs.isEmpty) {
        errorMsg =
            'Aucun mareyeur validé disponible dans la base de données. '
            'Veuillez contacter l\'administrateur pour ajouter des mareyeurs.';
      }

      if (mounted) {
        setState(() {
          _maryeurs = maryeurs;
          _isLoading = false;

          if (_maryeurs.isNotEmpty) {
            _selectedMaryeur = _maryeurs[0];
          } else {
            _errorMessage = errorMsg;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _maryeurs = [];
          _selectedMaryeur = null;

          if (e.toString().contains('network') ||
              e.toString().contains('connexion') ||
              e.toString().contains('SocketException') ||
              e.toString().contains('timeout')) {
            _errorMessage =
                'Problème de connexion au serveur. Vérifiez que le serveur backend est en cours d\'exécution et que votre connexion internet fonctionne.';
          } else {
            _errorMessage =
                'Erreur lors du chargement des mareyeurs: ${e.toString()}. Veuillez réessayer ou contacter l\'administrateur.';
          }
        });
      }
    }
  }

  // Charger la liste des vétérinaires disponibles
  Future<void> _loadVeterinaires() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final user = await UnifiedAuthService().getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non connecté');
      }

      final apiService = UnifiedApiService();
      final isConnected = await apiService.checkServerConnectivity();
      if (!isConnected) {
        throw Exception('Impossible de se connecter au serveur');
      }

      final veterinairesData = await apiService.getAllVeterinaires();

      // Convertir les objets Veterinaire en Map pour la compatibilité
      final veterinaires = <Map<String, dynamic>>[];

      for (final vet in veterinairesData) {
        final mongoId = vet.id;
        final nom = vet.nom;
        final prenom = vet.prenom;
        final isValidated = vet.isValidated;

        if (mongoId == null ||
            mongoId.isEmpty ||
            nom.isEmpty ||
            prenom.isEmpty) {
          continue;
        }

        final veterinaireMap = {
          '_id': mongoId,
          'nom': nom,
          'prenom': prenom,
          'email': vet.email,
          'telephone': vet.telephone ?? '',
          'port': vet.port ?? '',
          'matricule': vet.matricule ?? '',
          'isValidated': isValidated,
        };

        veterinaires.add(veterinaireMap);
      }

      String? errorMsg;
      if (veterinaires.isEmpty) {
        errorMsg =
            'Aucun vétérinaire validé disponible dans la base de données. '
            'Veuillez contacter l\'administrateur pour ajouter des vétérinaires.';
      }

      if (mounted) {
        setState(() {
          _veterinaires = veterinaires;
          _isLoading = false;

          if (_veterinaires.isNotEmpty) {
            _selectedVeterinaire = _veterinaires[0];
          } else {
            _errorMessage = errorMsg;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _veterinaires = [];
          _selectedVeterinaire = null;

          if (e.toString().contains('network') ||
              e.toString().contains('connexion') ||
              e.toString().contains('SocketException') ||
              e.toString().contains('timeout')) {
            _errorMessage =
                'Problème de connexion au serveur. Vérifiez que le serveur backend est en cours d\'exécution et que votre connexion internet fonctionne.';
          } else {
            _errorMessage =
                'Erreur lors du chargement des vétérinaires: ${e.toString()}. Veuillez réessayer ou contacter l\'administrateur.';
          }
        });
      }
    }
  }

  // Méthode utilitaire pour convertir les IDs MongoDB en string
  String _convertIdToString(dynamic id) {
    if (id == null) return '';

    if (id is String) {
      return id;
    } else if (id is Map && id.containsKey('\$oid')) {
      return id['\$oid'].toString();
    } else {
      return id.toString();
    }
  }

  // Méthode pour rafraîchir toutes les données
  Future<void> _refreshAllData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    await Future.wait([_loadMaryeurs(), _loadVeterinaires()]);

    setState(() {
      _isLoading = false;
    });
  }

  // Méthode simplifiée pour enregistrer les données du poisson
  Future<void> _saveFishData() async {
    // Vérifier que le formulaire est valide
    if (!_formKey.currentState!.validate()) return;

    // Vérifier que le mareyeur et le vétérinaire sont sélectionnés
    if (_selectedMaryeur == null) {
      setState(() {
        _errorMessage = 'Veuillez sélectionner un mareyeur';
      });
      return;
    }

    if (_selectedVeterinaire == null) {
      setState(() {
        _errorMessage = 'Veuillez sélectionner un vétérinaire';
      });
      return;
    }

    // Afficher un indicateur de chargement
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _successMessage = 'Enregistrement en cours...';
    });

    try {
      final user = await UnifiedAuthService().getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non connecté');
      }

      // Extraire les IDs du mareyeur et du vétérinaire
      final maryeurId = _selectedMaryeur!['_id']?.toString() ?? '';
      final veterinaireId = _selectedVeterinaire!['_id']?.toString() ?? '';

      if (maryeurId.isEmpty || veterinaireId.isEmpty) {
        throw Exception('IDs du mareyeur ou du vétérinaire invalides');
      }

      // Télécharger l'image sur le serveur
      setState(() {
        _successMessage = "Téléchargement de l'image en cours...";
      });

      final imageUrl = await UnifiedApiService().uploadImage(widget.imageFile);

      setState(() {
        _successMessage =
            "Image téléchargée avec succès. Création de la prise en cours...";
      });

      // Préparer les données du lot
      final now = DateTime.now();
      final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');

      final lotData = {
        // Identifiants et métadonnées
        'identifiant': 'LOT-${now.millisecondsSinceEpoch}',
        'nom': 'Lot de ${widget.especeNom}',
        'dateSoumission': dateFormat.format(now),

        // Informations sur le poisson
        'photo': imageUrl,
        'poids': double.parse(_poidController.text),
        'temperature': double.parse(_temperatureController.text),
        'quantite': 1,

        // Informations sur l'espèce
        'especeNom': widget.especeNom,

        // Informations sur la pêche
        'engin': _enginController.text,
        'zone': _zoneController.text,
        'lieu':
            _emplacementController.text.isNotEmpty
                ? _emplacementController.text
                : 'Non spécifié',

        // Acteurs concernés
        'pecheur': user.id,
        'veterinaire': veterinaireId,
        'maryeur': maryeurId,

        // Statuts
        'isValidated': false,
        'isRejected': false,
        'isAuctioned': false,
        'isSold': false,
      };

      // Créer le lot via l'API
      final response = await UnifiedApiService().post('lots', lotData);
      final lotId = response['data']['_id'] ?? response['data']['id'];

      // Notifier le vétérinaire
      final veterinaire = _selectedVeterinaire!['_id'];

      try {
        await UnifiedApiService().post('notifications', {
          'destinataire': veterinaire,
          'destinataireType': 'Veterinaire',
          'titre': 'Nouveau lot à valider',
          'contenu':
              'Un nouveau lot de ${widget.especeNom} vous a été assigné pour validation.',
          'type': 'info',
          'reference': lotId,
          'referenceModel': 'Lot',
          'isRead': false,
          'dateCreation': dateFormat.format(now),
        });
      } catch (notificationError) {
        // Ignorer les erreurs de notification
      }

      // Afficher un message de succès et retourner à l'écran principal
      setState(() {
        _isLoading = false;
        _successMessage = "Lot créé avec succès!";
      });

      if (!mounted) return;

      // Afficher un message de succès
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white),
              SizedBox(width: 8),
              Text('Poisson enregistré avec succès!'),
            ],
          ),
          backgroundColor: Theme.of(context).colorScheme.secondary,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          duration: const Duration(seconds: 3),
        ),
      );

      // Retourner à l'écran principal
      Navigator.of(context).popUntil((route) => route.isFirst);
    } catch (e) {
      String errorMessage = 'Erreur lors de l\'enregistrement du poisson';

      if (e.toString().contains('network') ||
          e.toString().contains('connexion') ||
          e.toString().contains('SocketException') ||
          e.toString().contains('timeout')) {
        errorMessage =
            'Problème de connexion au serveur. Vérifiez votre connexion internet et réessayez.';
      } else if (e.toString().contains('validation')) {
        errorMessage =
            'Erreur de validation des données. Vérifiez que tous les champs sont correctement remplis.';
      } else if (e.toString().contains('upload')) {
        errorMessage =
            'Erreur lors du téléchargement de l\'image. Vérifiez votre connexion et réessayez.';
      } else if (e.toString().contains('Utilisateur non connecté')) {
        errorMessage = 'Session expirée. Veuillez vous reconnecter.';
      } else if (e.toString().contains('IDs')) {
        errorMessage =
            'Erreur avec les identifiants sélectionnés. Veuillez réessayer la sélection.';
      } else {
        errorMessage = 'Erreur inattendue: ${e.toString()}';
      }

      setState(() {
        _errorMessage = errorMessage;
        _isLoading = false;
        _successMessage = null;
      });

      // Afficher un message d'erreur
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text(errorMessage)),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.primaryColor;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Détails du poisson'),
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: _responsiveService.adaptivePadding(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: _animationService.staggeredList([
              // Bouton de rafraîchissement global
              if (_errorMessage != null)
                Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: theme.colorScheme.error.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: theme.colorScheme.error,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'Problème de chargement des données',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: theme.colorScheme.error,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _errorMessage ??
                            'Une erreur est survenue lors du chargement des données.',
                        style: theme.textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: CustomButton.outline(
                          text: 'Actualiser les données',
                          icon: Icons.refresh,
                          onPressed: _refreshAllData,
                          color: theme.colorScheme.error,
                        ),
                      ),
                    ],
                  ),
                ),

              // Image et informations de base
              SeaCard(
                child: Column(
                  children: [
                    Row(
                      children: [
                        // Image du poisson
                        ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.file(
                            widget.imageFile,
                            width: 120,
                            height: 120,
                            fit: BoxFit.cover,
                          ),
                        ),
                        const SizedBox(width: 16),
                        // Informations de base
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.especeNom,
                                style: theme.textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: primaryColor,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                widget.especeNom,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                              const SizedBox(height: 12),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.secondary.withValues(
                                    alpha: 0.1,
                                  ),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  'Identification automatique',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.secondary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              SeaCard(
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SeaSectionHeader(title: 'Informations de capture'),

                      // Weight
                      TextFormField(
                        controller: _poidController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: 'Poids (kg)',
                          prefixIcon: Icon(Icons.scale),
                          hintText: 'Ex: 2.5',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez entrer le poids';
                          }
                          if (double.tryParse(value) == null) {
                            return 'Veuillez entrer un nombre valide';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Temperature
                      TextFormField(
                        controller: _temperatureController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: 'Température (°C)',
                          prefixIcon: Icon(Icons.thermostat),
                          hintText: 'Ex: 4',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez entrer la température';
                          }
                          if (double.tryParse(value) == null) {
                            return 'Veuillez entrer un nombre valide';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Fishing method
                      DropdownButtonFormField<String>(
                        value: _enginController.text,
                        decoration: const InputDecoration(
                          labelText: 'Méthode de pêche',
                          prefixIcon: Icon(Icons.sailing),
                        ),
                        items:
                            _methodesDepeche.map((String method) {
                              return DropdownMenuItem<String>(
                                value: method,
                                child: Text(method),
                              );
                            }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            setState(() {
                              _enginController.text = newValue;
                            });
                          }
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez sélectionner une méthode de pêche';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Fishing zone
                      DropdownButtonFormField<String>(
                        value: _zoneController.text,
                        decoration: const InputDecoration(
                          labelText: 'Zone de pêche',
                          prefixIcon: Icon(Icons.map),
                        ),
                        items:
                            _zonesDepeche.map((String zone) {
                              return DropdownMenuItem<String>(
                                value: zone,
                                child: Text(zone),
                              );
                            }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            setState(() {
                              _zoneController.text = newValue;
                            });
                          }
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez sélectionner une zone de pêche';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ]),
          ),
        ),
      ),
    );
  }
}
