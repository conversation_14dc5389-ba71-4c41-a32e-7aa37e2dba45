import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:dio/dio.dart';
import 'package:path/path.dart' as path;
import '../utils/error_handler.dart';
import '../utils/app_error.dart';
import '../models/pecheur.dart';
import '../models/veterinaire.dart';
import '../models/maryeur.dart';
import '../models/client.dart';
import '../models/lot.dart';
import '../services/model_service.dart';
import '../services/service_config.dart';
import '../services/network_discovery_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http_parser/http_parser.dart';

/// Service API unifié qui combine les fonctionnalités de ApiService et ApiServiceImproved
class UnifiedApiService {
  static final UnifiedApiService _instance = UnifiedApiService._internal();
  factory UnifiedApiService() => _instance;

  // Configuration des services
  final ServiceConfig _config = ServiceConfig();

  // URL de base de l'API
  String get _baseUrl => _config.apiBaseUrl;

  // Token d'authentification
  String? _authToken;

  // Clé pour le token d'authentification dans les préférences
  static const String _tokenKey = 'auth_token';

  // Service de modèle pour standardiser les données
  final ModelService _modelService = ModelService();

  // Client HTTP pour les requêtes
  final http.Client _httpClient = http.Client();

  // Client Dio pour les requêtes avancées (upload de fichiers, etc.)
  final Dio _dio = Dio();

  UnifiedApiService._internal() {
    // Initialiser le service
    _initService();
  }

  /// Initialiser le service
  Future<void> _initService() async {
    try {
      // Récupérer le token d'authentification depuis les préférences
      final prefs = await SharedPreferences.getInstance();
      _authToken = prefs.getString(_tokenKey);

      // Récupérer l'adresse IP du serveur qui a fonctionné précédemment
      final savedIp = prefs.getString('working_server_ip');
      if (savedIp != null && savedIp.isNotEmpty) {
        // Utiliser l'adresse IP sauvegardée
        final newBaseUrl = 'http://$savedIp:3005';
        _customBaseUrl = newBaseUrl;

        ErrorHandler.instance.logInfo(
          'Utilisation de l\'adresse IP sauvegardée: $savedIp',
          context: 'UnifiedApiService._initService',
        );
      }

      // Configurer Dio
      _dio.options.baseUrl = getBaseUrl();
      _dio.options.connectTimeout = Duration(seconds: _config.defaultTimeout);
      _dio.options.receiveTimeout = Duration(seconds: _config.defaultTimeout);

      // Ajouter les intercepteurs pour la gestion des erreurs
      _dio.interceptors.add(
        InterceptorsWrapper(
          onRequest: (options, handler) {
            // Ajouter le token d'authentification si disponible
            if (_authToken != null) {
              options.headers['Authorization'] = 'Bearer $_authToken';
            }
            return handler.next(options);
          },
          onError: (DioException e, handler) {
            // Gérer les erreurs
            ErrorHandler.instance.logError(e, context: 'UnifiedApiService.dio');
            return handler.next(e);
          },
        ),
      );

      // Journaliser l'initialisation
      ErrorHandler.instance.logInfo(
        'Service API unifié initialisé avec l\'URL: ${getBaseUrl()}',
        context: 'UnifiedApiService',
      );
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService._initService',
      );
    }
  }

  // URL de base personnalisée (si différente de la configuration)
  String? _customBaseUrl;

  /// Définir l'URL de base de l'API
  void setBaseUrl(String url) {
    _customBaseUrl = url.endsWith('/') ? url : '$url/';
    _dio.options.baseUrl = _customBaseUrl!;

    // Journaliser le changement
    ErrorHandler.instance.logInfo(
      'URL de base de l\'API modifiée: $_customBaseUrl',
      context: 'UnifiedApiService',
    );
  }

  /// Obtenir l'URL de base de l'API
  String getBaseUrl() {
    return _customBaseUrl ?? _baseUrl;
  }

  /// Obtenir l'URL complète d'une image
  String getImageUrl(String path) {
    if (path.startsWith('http://') || path.startsWith('https://')) {
      return path;
    }

    // Construire l'URL de l'image
    final baseUrl = getBaseUrl().replaceAll('/api/', '').replaceAll('/api', '');
    return '$baseUrl/$path';
  }

  /// Télécharger une image sur le serveur

  Future<String> uploadImage(File imageFile) async {
    try {
      // Utiliser l'URL de base dynamique pour l'émulateur
      final baseUrl = getBaseUrl();
      final uri = Uri.parse('$baseUrl/images/upload');

      // Utiliser le token d'authentification stocké
      final token = _authToken;

      final request = http.MultipartRequest('POST', uri);

      // Ajouter le token d'authentification s'il est disponible
      if (token != null) {
        request.headers['Authorization'] = 'Bearer $token';
      }

      request.files.add(
        await http.MultipartFile.fromPath(
          'image',
          imageFile.path,
          filename: path.basename(imageFile.path),
          contentType: MediaType('image', 'heic'), // 👈 Set the MIME type here
        ),
      );

      final response = await request.send();
      final respStr = await response.stream.bytesToString();

      // Codes de succès : 200 (OK) et 201 (Created)
      if (response.statusCode == 200 || response.statusCode == 201) {
        final Map<String, dynamic> jsonResp = jsonDecode(respStr);

        // Essayer différents champs pour l'URL de l'image
        String? imageUrl =
            jsonResp['imageUrl'] ??
            jsonResp['photo'] ??
            jsonResp['data']?['path'] ??
            jsonResp['path'];

        if (imageUrl != null && imageUrl.isNotEmpty) {
          return imageUrl;
        } else {
          throw Exception(
            'Aucune URL d\'image trouvée dans la réponse: ${jsonResp.toString()}',
          );
        }
      } else {
        throw Exception('Erreur serveur : ${response.statusCode} - $respStr');
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Télécharger un fichier sur le serveur
  Future<String?> uploadFile(String endpoint, String filePath) async {
    try {
      // Créer un FormData pour l'upload de fichier
      final file = File(filePath);
      final fileName = path.basename(filePath);

      // Vérifier que le fichier existe
      if (!await file.exists()) {
        throw Exception('Le fichier n\'existe pas: $filePath');
      }

      // Vérifier la taille du fichier
      final fileSize = await file.length();
      ErrorHandler.instance.logInfo(
        'Taille du fichier à télécharger: ${fileSize / 1024} KB',
        context: 'UnifiedApiService.uploadFile',
      );

      // Utiliser Dio pour l'upload de fichier
      final formData = FormData.fromMap({
        'image': await MultipartFile.fromFile(file.path, filename: fileName),
      });

      // Configurer les headers
      final options = Options(
        headers: {
          'Content-Type': 'multipart/form-data',
          if (_authToken != null) 'Authorization': 'Bearer $_authToken',
        },
        receiveTimeout: const Duration(seconds: 60),
        sendTimeout: const Duration(seconds: 60),
      );

      // Construire l'URL correctement
      final baseUrl = getBaseUrl();
      final normalizedEndpoint =
          endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
      final fullUrl = '$baseUrl$normalizedEndpoint';

      // Journaliser l'URL pour le débogage
      ErrorHandler.instance.logInfo(
        'Upload de fichier vers: $fullUrl',
        context: 'UnifiedApiService.uploadFile',
      );

      // Envoyer la requête
      final response = await _dio.post(
        fullUrl,
        data: formData,
        options: options,
        onSendProgress: (sent, total) {
          final progress = (sent / total * 100).toStringAsFixed(2);
          ErrorHandler.instance.logInfo(
            'Progression de l\'upload: $progress% ($sent/$total)',
            context: 'UnifiedApiService.uploadFile',
          );
        },
      );

      // Journaliser la réponse complète pour le débogage
      ErrorHandler.instance.logInfo(
        'Réponse du serveur: ${response.data}',
        context: 'UnifiedApiService.uploadFile',
      );

      // Traiter la réponse
      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = response.data;

        // Extraire le chemin du fichier de la réponse
        if (data is Map && data.containsKey('filename')) {
          return data['filename'];
        } else if (data is Map && data.containsKey('imageUrl')) {
          return data['imageUrl'];
        } else if (data is Map && data.containsKey('path')) {
          return data['path'];
        } else if (data is Map &&
            data.containsKey('data') &&
            data['data'] is Map) {
          final dataMap = data['data'] as Map;
          return dataMap['path'] ?? dataMap['filename'] ?? dataMap['imageUrl'];
        }
      }

      // Si on arrive ici, c'est qu'on n'a pas pu extraire le chemin du fichier
      ErrorHandler.instance.logWarning(
        'Impossible d\'extraire le chemin du fichier de la réponse: ${response.data}',
        context: 'UnifiedApiService.uploadFile',
      );

      return null;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService.uploadFile',
      );
      return null;
    }
  }

  /// Encoder un fichier en base64
  Future<String> encodeFileToBase64(String filePath) async {
    try {
      final file = File(filePath);
      final bytes = await file.readAsBytes();
      return base64Encode(bytes);
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService.encodeFileToBase64',
      );
      throw Exception('Erreur lors de l\'encodage du fichier: $e');
    }
  }

  /// Définir le token d'authentification
  void setAuthToken(String? token) {
    _authToken = token;

    // Journaliser le changement
    if (token != null) {
      ErrorHandler.instance.logInfo(
        'Token d\'authentification défini',
        context: 'UnifiedApiService',
      );
    } else {
      ErrorHandler.instance.logInfo(
        'Token d\'authentification supprimé',
        context: 'UnifiedApiService',
      );
    }
  }

  /// Vérifier la connectivité au serveur
  Future<bool> checkServerConnectivity() async {
    try {
      final baseUrl = getBaseUrl();
      final baseUrlWithoutApi = baseUrl.replaceAll('/api', '');

      // Journaliser l'URL utilisée pour la vérification
      ErrorHandler.instance.logInfo(
        'Vérification de la connectivité au serveur: $baseUrl',
        context: 'UnifiedApiService.checkServerConnectivity',
      );

      // Essayer l'endpoint de santé spécifique
      try {
        final healthUrl = '$baseUrlWithoutApi/api/health';
        final healthUri = Uri.parse(healthUrl);

        final healthResponse = await _httpClient
            .get(healthUri)
            .timeout(Duration(seconds: _config.defaultTimeout ~/ 2));

        if (healthResponse.statusCode >= 200 &&
            healthResponse.statusCode < 300) {
          ErrorHandler.instance.logInfo(
            'Connectivité au serveur vérifiée avec succès via /api/health',
            context: 'UnifiedApiService.checkServerConnectivity',
          );
          return true;
        }
      } catch (healthError) {
        // Si l'endpoint de santé échoue, essayer l'URL de base
        ErrorHandler.instance.logWarning(
          'Échec de la vérification via /api/health',
          context: 'UnifiedApiService.checkServerConnectivity',
        );
      }

      // Essayer l'URL de base comme fallback
      try {
        final rootUrl = baseUrlWithoutApi;
        final rootUri = Uri.parse(rootUrl);

        final response = await _httpClient
            .get(rootUri)
            .timeout(Duration(seconds: _config.defaultTimeout ~/ 2));

        final isConnected =
            response.statusCode >= 200 && response.statusCode < 300;

        if (isConnected) {
          ErrorHandler.instance.logInfo(
            'Connectivité au serveur vérifiée avec succès via l\'URL de base',
            context: 'UnifiedApiService.checkServerConnectivity',
          );
          return true;
        }
      } catch (rootError) {
        ErrorHandler.instance.logWarning(
          'Échec de la vérification via l\'URL de base',
          context: 'UnifiedApiService.checkServerConnectivity',
        );
      }

      // Utiliser le service de découverte réseau comme dernier recours
      try {
        final networkDiscoveryService = NetworkDiscoveryService();
        final discoveredServers =
            await networkDiscoveryService.loadDiscoveredServers();

        if (discoveredServers.isNotEmpty) {
          // Essayer de se connecter au premier serveur découvert
          final serverIp = discoveredServers.first;
          final connected = await networkDiscoveryService.connectToServer(
            serverIp,
          );

          if (connected) {
            ErrorHandler.instance.logInfo(
              'Connexion réussie via le serveur découvert: $serverIp',
              context: 'UnifiedApiService.checkServerConnectivity',
            );
            return true;
          }
        }
      } catch (discoveryError) {
        ErrorHandler.instance.logWarning(
          'Échec de la connexion via le service de découverte réseau',
          context: 'UnifiedApiService.checkServerConnectivity',
        );
      }

      return false;
    } catch (e) {
      ErrorHandler.instance.logWarning(
        'Impossible de se connecter au serveur',
        context: 'UnifiedApiService.checkServerConnectivity',
      );
      return false;
    }
  }

  /// Effectuer une requête GET
  Future<Map<String, dynamic>> get(
    String endpoint, {
    Map<String, String>? queryParameters,
  }) async {
    try {
      final baseUrl = getBaseUrl();

      // S'assurer que l'endpoint ne commence pas par un slash si baseUrl se termine par un slash
      final normalizedEndpoint =
          endpoint.startsWith('/') && baseUrl.endsWith('/')
              ? endpoint.substring(1)
              : endpoint;

      // Construire l'URL complète
      String fullUrl;
      String apiPrefix = '';

      // Vérifier si l'URL de base se termine par '/api'
      if (baseUrl.endsWith('/api')) {
        // Ajouter un slash entre '/api' et l'endpoint
        fullUrl = '$baseUrl/$normalizedEndpoint';
      } else {
        // S'assurer que l'endpoint commence par 'api/' si ce n'est pas déjà le cas
        // et si baseUrl ne contient pas déjà 'api/'
        if (!baseUrl.contains('/api/')) {
          apiPrefix = '/api/';
        }

        final endpointWithApiPrefix =
            normalizedEndpoint.startsWith('api/')
                ? normalizedEndpoint
                : '$apiPrefix$normalizedEndpoint';

        fullUrl = '$baseUrl$endpointWithApiPrefix';
      }

      // Journaliser l'URL complète pour le débogage
      ErrorHandler.instance.logInfo(
        'Requête GET: $fullUrl, Paramètres: $queryParameters',
        context: 'UnifiedApiService.get',
      );

      // Parser l'URL
      final uri = Uri.parse(fullUrl).replace(queryParameters: queryParameters);

      // Préparer les headers
      final headers = <String, String>{'Content-Type': 'application/json'};
      if (_authToken != null) {
        headers['Authorization'] = 'Bearer $_authToken';
      }

      // Envoyer la requête
      final response = await _httpClient
          .get(uri, headers: headers)
          .timeout(Duration(seconds: _config.defaultTimeout));

      // Traiter la réponse
      return _handleResponse(response);
    } catch (e) {
      ErrorHandler.instance.logError(e, context: 'UnifiedApiService.get');

      // Si l'erreur est liée à un problème de connectivité, essayer de découvrir le serveur
      if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection refused') ||
          e.toString().contains('Network is unreachable') ||
          e.toString().contains('Invalid port') ||
          e.toString().contains('FormatException')) {
        ErrorHandler.instance.logInfo(
          'Tentative de découverte du serveur après erreur de connectivité',
          context: 'UnifiedApiService.get',
        );

        try {
          // Utiliser le service de découverte réseau
          final networkDiscoveryService = NetworkDiscoveryService();
          final discoveredServers =
              await networkDiscoveryService.loadDiscoveredServers();

          if (discoveredServers.isNotEmpty) {
            // Essayer de se connecter au premier serveur découvert
            final serverIp = discoveredServers.first;
            final connected = await networkDiscoveryService.connectToServer(
              serverIp,
            );

            if (connected) {
              // Réessayer la requête
              return await get(endpoint, queryParameters: queryParameters);
            }
          }
        } catch (retryError) {
          ErrorHandler.instance.logError(
            'Échec de la tentative de découverte du serveur',
            context: 'UnifiedApiService.get',
          );
        }
      }

      rethrow;
    }
  }

  /// Effectuer une requête POST
  Future<Map<String, dynamic>> post(String endpoint, dynamic data) async {
    try {
      final baseUrl = getBaseUrl();

      // S'assurer que l'endpoint ne commence pas par un slash si baseUrl se termine par un slash
      final normalizedEndpoint =
          endpoint.startsWith('/') && baseUrl.endsWith('/')
              ? endpoint.substring(1)
              : endpoint;

      // Construire l'URL complète
      String fullUrl;
      String apiPrefix = '';

      // Vérifier si l'URL de base se termine par '/api'
      if (baseUrl.endsWith('/api')) {
        // Ajouter un slash entre '/api' et l'endpoint
        fullUrl = '$baseUrl/$normalizedEndpoint';
      } else {
        // S'assurer que l'endpoint commence par 'api/' si ce n'est pas déjà le cas
        // et si baseUrl ne contient pas déjà 'api/'
        if (!baseUrl.contains('/api/')) {
          apiPrefix = 'api/';
        }

        final endpointWithApiPrefix =
            normalizedEndpoint.startsWith('api/')
                ? normalizedEndpoint
                : '$apiPrefix$normalizedEndpoint';

        fullUrl = '$baseUrl$endpointWithApiPrefix';
      }

      // Journaliser l'URL complète pour le débogage
      ErrorHandler.instance.logInfo(
        'Requête POST: $fullUrl',
        context: 'UnifiedApiService.post',
      );

      // Parser l'URL
      final uri = Uri.parse(fullUrl);

      // Préparer les headers
      final headers = <String, String>{'Content-Type': 'application/json'};
      if (_authToken != null) {
        headers['Authorization'] = 'Bearer $_authToken';
      }

      // Envoyer la requête
      final response = await _httpClient
          .post(uri, headers: headers, body: json.encode(data))
          .timeout(Duration(seconds: _config.defaultTimeout));

      // Traiter la réponse
      return _handleResponse(response);
    } catch (e) {
      ErrorHandler.instance.logError(e, context: 'UnifiedApiService.post');

      // Si l'erreur est liée à un problème de connectivité, essayer de découvrir le serveur
      if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection refused') ||
          e.toString().contains('Network is unreachable') ||
          e.toString().contains('Invalid port') ||
          e.toString().contains('FormatException')) {
        ErrorHandler.instance.logInfo(
          'Tentative de découverte du serveur après erreur de connectivité',
          context: 'UnifiedApiService.post',
        );

        try {
          // Utiliser le service de découverte réseau
          final networkDiscoveryService = NetworkDiscoveryService();
          final discoveredServers =
              await networkDiscoveryService.loadDiscoveredServers();

          if (discoveredServers.isNotEmpty) {
            // Essayer de se connecter au premier serveur découvert
            final serverIp = discoveredServers.first;
            final connected = await networkDiscoveryService.connectToServer(
              serverIp,
            );

            if (connected) {
              // Réessayer la requête
              return await post(endpoint, data);
            }
          }
        } catch (retryError) {
          ErrorHandler.instance.logError(
            'Échec de la tentative de découverte du serveur',
            context: 'UnifiedApiService.post',
          );
        }
      }

      rethrow;
    }
  }

  /// Effectuer une requête PUT
  Future<Map<String, dynamic>> put(String endpoint, dynamic data) async {
    try {
      final baseUrl = getBaseUrl();

      // S'assurer que l'endpoint ne commence pas par un slash si baseUrl se termine par un slash
      final normalizedEndpoint =
          endpoint.startsWith('/') && baseUrl.endsWith('/')
              ? endpoint.substring(1)
              : endpoint;

      // Construire l'URL complète
      String fullUrl;
      String apiPrefix = '';

      // Vérifier si l'URL de base se termine par '/api'
      if (baseUrl.endsWith('/api')) {
        // Ajouter un slash entre '/api' et l'endpoint
        fullUrl = '$baseUrl/$normalizedEndpoint';
      } else {
        // S'assurer que l'endpoint commence par 'api/' si ce n'est pas déjà le cas
        // et si baseUrl ne contient pas déjà 'api/'
        if (!baseUrl.contains('/api/')) {
          apiPrefix = '/api/';
        }

        final endpointWithApiPrefix =
            normalizedEndpoint.startsWith('api/')
                ? normalizedEndpoint
                : '$apiPrefix$normalizedEndpoint';

        fullUrl = '$baseUrl$endpointWithApiPrefix';
      }

      // Journaliser l'URL complète pour le débogage
      ErrorHandler.instance.logInfo(
        'Requête PUT: $fullUrl',
        context: 'UnifiedApiService.put',
      );

      // Parser l'URL
      final uri = Uri.parse(fullUrl);

      // Préparer les headers
      final headers = <String, String>{'Content-Type': 'application/json'};
      if (_authToken != null) {
        headers['Authorization'] = 'Bearer $_authToken';
      }

      // Envoyer la requête
      final response = await _httpClient
          .put(uri, headers: headers, body: json.encode(data))
          .timeout(Duration(seconds: _config.defaultTimeout));

      // Traiter la réponse
      return _handleResponse(response);
    } catch (e) {
      ErrorHandler.instance.logError(e, context: 'UnifiedApiService.put');

      // Si l'erreur est liée à un problème de connectivité, essayer de découvrir le serveur
      if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection refused') ||
          e.toString().contains('Network is unreachable') ||
          e.toString().contains('Invalid port') ||
          e.toString().contains('FormatException')) {
        ErrorHandler.instance.logInfo(
          'Tentative de découverte du serveur après erreur de connectivité',
          context: 'UnifiedApiService.put',
        );

        try {
          // Utiliser le service de découverte réseau
          final networkDiscoveryService = NetworkDiscoveryService();
          final discoveredServers =
              await networkDiscoveryService.loadDiscoveredServers();

          if (discoveredServers.isNotEmpty) {
            // Essayer de se connecter au premier serveur découvert
            final serverIp = discoveredServers.first;
            final connected = await networkDiscoveryService.connectToServer(
              serverIp,
            );

            if (connected) {
              // Réessayer la requête
              return await put(endpoint, data);
            }
          }
        } catch (retryError) {
          ErrorHandler.instance.logError(
            'Échec de la tentative de découverte du serveur',
            context: 'UnifiedApiService.put',
          );
        }
      }

      rethrow;
    }
  }

  /// Effectuer une requête PATCH
  Future<Map<String, dynamic>> patch(String endpoint, dynamic data) async {
    try {
      final baseUrl = getBaseUrl();

      // S'assurer que l'endpoint ne commence pas par un slash si baseUrl se termine par un slash
      final normalizedEndpoint =
          endpoint.startsWith('/') && baseUrl.endsWith('/')
              ? endpoint.substring(1)
              : endpoint;

      // Construire l'URL complète
      String fullUrl;
      String apiPrefix = '';

      // Vérifier si l'URL de base se termine par '/api'
      if (baseUrl.endsWith('/api')) {
        // Ajouter un slash entre '/api' et l'endpoint
        fullUrl = '$baseUrl/$normalizedEndpoint';
      } else {
        // S'assurer que l'endpoint commence par 'api/' si ce n'est pas déjà le cas
        // et si baseUrl ne contient pas déjà 'api/'
        if (!baseUrl.contains('/api/')) {
          apiPrefix = '/api/';
        }

        final endpointWithApiPrefix =
            normalizedEndpoint.startsWith('api/')
                ? normalizedEndpoint
                : '$apiPrefix$normalizedEndpoint';

        fullUrl = '$baseUrl$endpointWithApiPrefix';
      }

      // Journaliser l'URL complète pour le débogage
      ErrorHandler.instance.logInfo(
        'Requête PATCH: $fullUrl',
        context: 'UnifiedApiService.patch',
      );

      // Parser l'URL
      final uri = Uri.parse(fullUrl);

      // Préparer les headers
      final headers = <String, String>{'Content-Type': 'application/json'};
      if (_authToken != null) {
        headers['Authorization'] = 'Bearer $_authToken';
      }

      // Envoyer la requête
      final response = await _httpClient
          .patch(uri, headers: headers, body: json.encode(data))
          .timeout(Duration(seconds: _config.defaultTimeout));

      // Traiter la réponse
      return _handleResponse(response);
    } catch (e) {
      ErrorHandler.instance.logError(e, context: 'UnifiedApiService.patch');

      // Si l'erreur est liée à un problème de connectivité, essayer de découvrir le serveur
      if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection refused') ||
          e.toString().contains('Network is unreachable') ||
          e.toString().contains('Invalid port') ||
          e.toString().contains('FormatException')) {
        ErrorHandler.instance.logInfo(
          'Tentative de découverte du serveur après erreur de connectivité',
          context: 'UnifiedApiService.patch',
        );

        try {
          // Utiliser le service de découverte réseau
          final networkDiscoveryService = NetworkDiscoveryService();
          final discoveredServers =
              await networkDiscoveryService.loadDiscoveredServers();

          if (discoveredServers.isNotEmpty) {
            // Essayer de se connecter au premier serveur découvert
            final serverIp = discoveredServers.first;
            final connected = await networkDiscoveryService.connectToServer(
              serverIp,
            );

            if (connected) {
              // Réessayer la requête
              return await patch(endpoint, data);
            }
          }
        } catch (retryError) {
          ErrorHandler.instance.logError(
            'Échec de la tentative de découverte du serveur',
            context: 'UnifiedApiService.patch',
          );
        }
      }

      rethrow;
    }
  }

  /// Effectuer une requête DELETE
  Future<Map<String, dynamic>> delete(String endpoint) async {
    try {
      final baseUrl = getBaseUrl();

      // S'assurer que l'endpoint ne commence pas par un slash si baseUrl se termine par un slash
      final normalizedEndpoint =
          endpoint.startsWith('/') && baseUrl.endsWith('/')
              ? endpoint.substring(1)
              : endpoint;

      // Construire l'URL complète
      String fullUrl;
      String apiPrefix = '';

      // Vérifier si l'URL de base se termine par '/api'
      if (baseUrl.endsWith('/api')) {
        // Ajouter un slash entre '/api' et l'endpoint
        fullUrl = '$baseUrl/$normalizedEndpoint';
      } else {
        // S'assurer que l'endpoint commence par 'api/' si ce n'est pas déjà le cas
        // et si baseUrl ne contient pas déjà 'api/'
        if (!baseUrl.contains('/api/')) {
          apiPrefix = '/api/';
        }

        final endpointWithApiPrefix =
            normalizedEndpoint.startsWith('api/')
                ? normalizedEndpoint
                : '$apiPrefix$normalizedEndpoint';

        fullUrl = '$baseUrl$endpointWithApiPrefix';
      }

      // Journaliser l'URL complète pour le débogage
      ErrorHandler.instance.logInfo(
        'Requête DELETE: $fullUrl',
        context: 'UnifiedApiService.delete',
      );

      // Parser l'URL
      final uri = Uri.parse(fullUrl);

      // Préparer les headers
      final headers = <String, String>{'Content-Type': 'application/json'};
      if (_authToken != null) {
        headers['Authorization'] = 'Bearer $_authToken';
      }

      // Envoyer la requête
      final response = await _httpClient
          .delete(uri, headers: headers)
          .timeout(Duration(seconds: _config.defaultTimeout));

      // Traiter la réponse
      return _handleResponse(response);
    } catch (e) {
      ErrorHandler.instance.logError(e, context: 'UnifiedApiService.delete');

      // Si l'erreur est liée à un problème de connectivité, essayer de découvrir le serveur
      if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection refused') ||
          e.toString().contains('Network is unreachable') ||
          e.toString().contains('Invalid port') ||
          e.toString().contains('FormatException')) {
        ErrorHandler.instance.logInfo(
          'Tentative de découverte du serveur après erreur de connectivité',
          context: 'UnifiedApiService.delete',
        );

        try {
          // Utiliser le service de découverte réseau
          final networkDiscoveryService = NetworkDiscoveryService();
          final discoveredServers =
              await networkDiscoveryService.loadDiscoveredServers();

          if (discoveredServers.isNotEmpty) {
            // Essayer de se connecter au premier serveur découvert
            final serverIp = discoveredServers.first;
            final connected = await networkDiscoveryService.connectToServer(
              serverIp,
            );

            if (connected) {
              // Réessayer la requête
              return await delete(endpoint);
            }
          }
        } catch (retryError) {
          ErrorHandler.instance.logError(
            'Échec de la tentative de découverte du serveur',
            context: 'UnifiedApiService.delete',
          );
        }
      }

      rethrow;
    }
  }

  /// Gérer la réponse HTTP
  ///
  /// Cette méthode analyse la réponse HTTP et gère les différents cas d'erreur.
  /// Elle standardise également la structure de la réponse pour faciliter son traitement.
  ///
  /// @param response La réponse HTTP à traiter
  /// @return Une Map contenant les données de la réponse standardisée
  /// @throws AppError Si la réponse contient une erreur
  Map<String, dynamic> _handleResponse(http.Response response) {
    // La réponse ne peut pas être null en Dart non-nullable, mais nous vérifions quand même
    // les propriétés essentielles pour plus de robustesse

    final statusCode = response.statusCode;
    final responseBody = utf8.decode(response.bodyBytes);

    // Journaliser la réponse pour le débogage
    ErrorHandler.instance.logInfo(
      'Réponse HTTP (${response.statusCode}): ${responseBody.substring(0, responseBody.length > 100 ? 100 : responseBody.length)}...',
      context: 'UnifiedApiService._handleResponse',
    );

    // Vérifier si la réponse est du HTML au lieu du JSON
    if (responseBody.trim().startsWith('<!DOCTYPE') ||
        responseBody.trim().startsWith('<html') ||
        responseBody.contains('<body')) {
      // Extraire un message d'erreur plus utile du HTML si possible
      String htmlErrorMessage =
          'Le serveur a renvoyé une page HTML au lieu d\'une réponse JSON';

      // Essayer d'extraire le titre de la page HTML
      final titleMatch = RegExp(
        r'<title>(.*?)</title>',
      ).firstMatch(responseBody);
      if (titleMatch != null && titleMatch.group(1) != null) {
        htmlErrorMessage += ': ${titleMatch.group(1)}';
      }

      // Essayer d'extraire un message d'erreur plus spécifique
      final errorMatch = RegExp(
        r'<pre>(.*?)</pre>',
        dotAll: true,
      ).firstMatch(responseBody);
      if (errorMatch != null && errorMatch.group(1) != null) {
        htmlErrorMessage += '\nDétails: ${errorMatch.group(1)}';
      }

      // Journaliser l'URL qui a causé l'erreur
      ErrorHandler.instance.logError(
        'Réponse HTML reçue au lieu de JSON: $htmlErrorMessage\nURL: ${response.request?.url}',
        context: 'UnifiedApiService._handleResponse',
      );

      // Si c'est une erreur 404, retourner une réponse vide plutôt que de lancer une exception
      if (statusCode == 404) {
        ErrorHandler.instance.logWarning(
          'Ressource non trouvée (404), retour d\'une réponse vide',
          context: 'UnifiedApiService._handleResponse',
        );
        return {
          'success': false,
          'message': 'Ressource non trouvée',
          'status': 'error',
        };
      }

      throw AppError(
        message: 'Erreur de format de réponse: $htmlErrorMessage',
        type: ErrorType.server,
        context: 'UnifiedApiService._handleResponse',
      );
    }

    try {
      // Vérifier si la réponse est vide
      if (responseBody.isEmpty) {
        if (statusCode >= 200 && statusCode < 300) {
          return {'success': true, 'message': 'Opération réussie'};
        } else {
          throw AppError(
            message: 'Réponse vide avec code d\'état $statusCode',
            type: _getErrorType(statusCode),
            context: 'UnifiedApiService._handleResponse',
          );
        }
      }

      final jsonResponse = json.decode(responseBody) as Map<String, dynamic>;

      if (statusCode >= 200 && statusCode < 300) {
        // Vérifier si la réponse est déjà au format standardisé
        if (jsonResponse.containsKey('success')) {
          // La réponse est déjà standardisée, la retourner telle quelle
          return jsonResponse;
        }

        // Standardiser la structure de la réponse
        Map<String, dynamic> standardizedResponse = {
          'success': true,
          'message': 'Opération réussie',
        };

        // Si la réponse contient un champ 'data', l'utiliser directement
        if (jsonResponse.containsKey('data')) {
          standardizedResponse['data'] = jsonResponse['data'];
        }
        // Sinon, considérer toute la réponse comme les données
        else {
          // Exclure certains champs spécifiques
          final excludedKeys = [
            'success',
            'message',
            'token',
            'status',
            'error',
          ];

          // Extraire le token s'il existe
          if (jsonResponse.containsKey('token')) {
            standardizedResponse['token'] = jsonResponse['token'];
          }

          // Extraire le message s'il existe
          if (jsonResponse.containsKey('message')) {
            standardizedResponse['message'] = jsonResponse['message'];
          }

          // Créer un objet data avec le reste des champs
          final dataFields = Map<String, dynamic>.fromEntries(
            jsonResponse.entries.where(
              (entry) => !excludedKeys.contains(entry.key),
            ),
          );

          if (dataFields.isNotEmpty) {
            standardizedResponse['data'] = dataFields;
          }
        }

        return standardizedResponse;
      } else {
        // Vérifier si la réponse d'erreur est déjà standardisée
        if (jsonResponse.containsKey('success') &&
            jsonResponse['success'] == false) {
          // Extraire le message d'erreur
          final errorMessage =
              jsonResponse['error'] ??
              jsonResponse['message'] ??
              'Erreur inconnue';

          throw AppError(
            message: errorMessage,
            type: _getErrorType(statusCode),
            context: 'UnifiedApiService._handleResponse',
            details: jsonResponse['details'],
          );
        } else {
          // Standardiser l'erreur
          final errorMessage =
              jsonResponse['error'] ??
              jsonResponse['message'] ??
              'Erreur avec code d\'état $statusCode';

          throw AppError(
            message: errorMessage,
            type: _getErrorType(statusCode),
            context: 'UnifiedApiService._handleResponse',
            details: jsonResponse['details'],
          );
        }
      }
    } catch (e) {
      if (e is AppError) rethrow;

      // Journaliser l'erreur complète pour le débogage
      ErrorHandler.instance.logError(
        'Erreur lors du traitement de la réponse: ${e.toString()}\nCorps de la réponse: ${responseBody.substring(0, responseBody.length > 200 ? 200 : responseBody.length)}...',
        context: 'UnifiedApiService._handleResponse',
      );

      // Gestion spéciale pour les erreurs de connectivité
      if (e.toString().contains('Connection refused') ||
          e.toString().contains('Network is unreachable') ||
          e.toString().contains('No route to host') ||
          statusCode == 0) {
        throw AppError(
          message:
              'Impossible de se connecter au serveur. Vérifiez que le serveur backend est démarré et que votre appareil est sur le même réseau.',
          type: ErrorType.network,
          originalError: e,
          context: 'UnifiedApiService._handleResponse',
        );
      }

      throw AppError(
        message: 'Erreur lors du traitement de la réponse: ${e.toString()}',
        type: ErrorType.unknown,
        originalError: e,
        context: 'UnifiedApiService._handleResponse',
      );
    }
  }

  /// Obtenir le type d'erreur en fonction du code de statut HTTP
  ErrorType _getErrorType(int statusCode) {
    if (statusCode == 400) return ErrorType.validation;
    if (statusCode == 401) return ErrorType.authentication;
    if (statusCode == 403) return ErrorType.authorization;
    if (statusCode == 404) return ErrorType.notFound;
    if (statusCode >= 500) return ErrorType.server;
    return ErrorType.unknown;
  }

  //
  // Méthodes spécifiques pour les utilisateurs
  //

  /// Récupérer l'utilisateur actuel
  Future<dynamic> getCurrentUser() async {
    try {
      // Vérifier d'abord la connectivité au serveur
      final isConnected = await checkServerConnectivity();
      if (!isConnected) {
        ErrorHandler.instance.logWarning(
          'Impossible de se connecter au serveur pour récupérer l\'utilisateur',
          context: 'UnifiedApiService.getCurrentUser',
        );
        throw AppError(
          message: 'Impossible de se connecter au serveur',
          type: ErrorType.network,
          context: 'UnifiedApiService.getCurrentUser',
        );
      }

      final response = await get('auth/me');

      // Standardiser la structure de réponse
      Map<String, dynamic> userData;
      if (response.containsKey('data') && response['data'] != null) {
        userData = response['data'];
      } else if (response.containsKey('user') && response['user'] != null) {
        userData = response['user'];
      } else {
        // Si aucune structure reconnue, utiliser la réponse directement
        userData = response;
      }

      return _modelService.createUserFromMap(
        _modelService.standardizeMap(userData),
      );
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService.getCurrentUser',
      );

      // Si l'erreur est liée à un format d'URL invalide, essayer de corriger l'URL
      if (e.toString().contains('Invalid port') ||
          e.toString().contains('FormatException')) {
        ErrorHandler.instance.logInfo(
          'Tentative de correction de l\'URL après erreur de format',
          context: 'UnifiedApiService.getCurrentUser',
        );

        // Essayer avec une URL corrigée
        try {
          // Réinitialiser l'URL de base à une valeur sûre
          setBaseUrl('http://172.16.49.110:3005/api');

          // Vérifier à nouveau la connectivité
          final isConnected = await checkServerConnectivity();
          if (isConnected) {
            // Réessayer la requête
            return await getCurrentUser();
          }
        } catch (retryError) {
          ErrorHandler.instance.logError(
            'Échec de la tentative de correction: $retryError',
            context: 'UnifiedApiService.getCurrentUser',
          );
        }
      }

      return null;
    }
  }

  /// Connexion
  Future<dynamic> login(String email, String password) async {
    try {
      // Vérifier d'abord la connectivité au serveur
      final isConnected = await checkServerConnectivity();
      if (!isConnected) {
        ErrorHandler.instance.logWarning(
          'Impossible de se connecter au serveur pour la connexion',
          context: 'UnifiedApiService.login',
        );
        throw AppError(
          message:
              'Impossible de se connecter au serveur. Vérifiez que le serveur backend est démarré et que votre appareil est sur le même réseau.',
          type: ErrorType.network,
          context: 'UnifiedApiService.login',
        );
      }

      final response = await post('auth/login', {
        'email': email,
        'password': password,
      });

      // Sauvegarder le token d'authentification
      if (response.containsKey('token')) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_tokenKey, response['token']);
        setAuthToken(response['token']);
      }

      // Standardiser la structure de réponse
      Map<String, dynamic> userData;
      if (response.containsKey('data') && response['data'] != null) {
        userData = response['data'];
      } else if (response.containsKey('user') && response['user'] != null) {
        userData = response['user'];
      } else {
        // Si aucune structure reconnue, utiliser la réponse directement sans les champs spéciaux
        userData = Map<String, dynamic>.from(response);
        // Supprimer les champs qui ne sont pas des données utilisateur
        userData.remove('token');
        userData.remove('success');
        userData.remove('message');
      }

      return _modelService.createUserFromMap(
        _modelService.standardizeMap(userData),
      );
    } catch (e) {
      ErrorHandler.instance.logError(e, context: 'UnifiedApiService.login');

      // Si l'erreur est liée à un format d'URL invalide, essayer de corriger l'URL
      if (e.toString().contains('Invalid port') ||
          e.toString().contains('FormatException')) {
        ErrorHandler.instance.logInfo(
          'Tentative de correction de l\'URL après erreur de format',
          context: 'UnifiedApiService.login',
        );

        // Essayer avec une URL corrigée
        try {
          // Réinitialiser l'URL de base à une valeur sûre
          setBaseUrl('http://172.16.49.110:3005/api');

          // Vérifier à nouveau la connectivité
          final isConnected = await checkServerConnectivity();
          if (isConnected) {
            // Réessayer la requête
            return await login(email, password);
          }
        } catch (retryError) {
          ErrorHandler.instance.logError(
            'Échec de la tentative de correction: $retryError',
            context: 'UnifiedApiService.login',
          );
        }
      }

      rethrow;
    }
  }

  /// Déconnexion
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    setAuthToken(null);
  }

  /// Récupérer un pêcheur par son ID
  Future<Pecheur?> getPecheur(String id) async {
    try {
      final response = await get('pecheurs/$id');
      if (response.containsKey('data') && response['data'] != null) {
        final userData = response['data'];
        return Pecheur.fromMap(_modelService.standardizeMap(userData));
      }
      return null;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService.getPecheur',
      );
      return null;
    }
  }

  /// Récupérer tous les pêcheurs
  Future<List<Pecheur>> getAllPecheurs() async {
    try {
      final response = await get('pecheurs');
      if (response.containsKey('data') && response['data'] is List) {
        final List<dynamic> usersData = response['data'];
        return usersData
            .map(
              (userData) =>
                  Pecheur.fromMap(_modelService.standardizeMap(userData)),
            )
            .toList();
      }
      return [];
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService.getAllPecheurs',
      );
      return [];
    }
  }

  /// Récupérer un vétérinaire par son ID
  Future<Veterinaire?> getVeterinaire(String id) async {
    try {
      final response = await get('veterinaires/$id');
      final userData = _extractData(response);
      if (userData != null && userData is Map<String, dynamic>) {
        return Veterinaire.fromMap(_modelService.standardizeMap(userData));
      }
      return null;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService.getVeterinaire',
      );
      return null;
    }
  }

  /// Récupérer tous les vétérinaires
  Future<List<Veterinaire>> getAllVeterinaires() async {
    try {
      // Journaliser l'appel API
      ErrorHandler.instance.logInfo(
        'Récupération de tous les vétérinaires...',
        context: 'UnifiedApiService.getAllVeterinaires',
      );

      // Vérifier d'abord la connectivité au serveur
      final isConnected = await checkServerConnectivity();
      if (!isConnected) {
        ErrorHandler.instance.logWarning(
          'Impossible de se connecter au serveur pour récupérer les vétérinaires',
          context: 'UnifiedApiService.getAllVeterinaires',
        );
        throw AppError(
          message:
              'Impossible de se connecter au serveur. Vérifiez que le serveur backend est démarré et que votre appareil est sur le même réseau.',
          type: ErrorType.network,
          context: 'UnifiedApiService.getAllVeterinaires',
        );
      }

      // APPROCHE SIMPLIFIÉE: Récupérer tous les vétérinaires sans filtre
      final response = await get('veterinaires');

      // Journaliser la réponse complète pour le débogage (limité à 500 caractères)
      final responseStr = response.toString();
      final truncatedResponse =
          responseStr.length > 500
              ? '${responseStr.substring(0, 500)}...'
              : responseStr;

      ErrorHandler.instance.logInfo(
        'Réponse API vétérinaires: $truncatedResponse',
        context: 'UnifiedApiService.getAllVeterinaires',
      );

      // Extraire les données de la réponse
      final List<dynamic> allUsersData = _extractDataList(response);

      // Filtrer manuellement les vétérinaires validés et non bloqués
      final List<dynamic> usersData =
          allUsersData
              .where(
                (item) =>
                    item is Map &&
                    (item['isValidated'] == true || item['isValid'] == true) &&
                    (item['isBlocked'] == false || item['isBlocked'] == null),
              )
              .toList();

      // Journaliser le nombre de vétérinaires récupérés
      ErrorHandler.instance.logInfo(
        'Nombre total de vétérinaires: ${allUsersData.length}, Nombre de vétérinaires validés et non bloqués: ${usersData.length}',
        context: 'UnifiedApiService.getAllVeterinaires',
      );

      if (usersData.isEmpty) {
        ErrorHandler.instance.logWarning(
          'Aucun vétérinaire validé et non bloqué trouvé',
          context: 'UnifiedApiService.getAllVeterinaires',
        );

        // Essayer avec une requête explicite avec paramètres
        ErrorHandler.instance.logInfo(
          'Tentative avec paramètres explicites...',
          context: 'UnifiedApiService.getAllVeterinaires',
        );

        final paramResponse = await get(
          'veterinaires',
          queryParameters: {'isValidated': 'true', 'isBlocked': 'false'},
        );

        final paramData = _extractDataList(paramResponse);

        if (paramData.isNotEmpty) {
          ErrorHandler.instance.logInfo(
            'Récupération avec paramètres réussie: ${paramData.length} vétérinaires trouvés',
            context: 'UnifiedApiService.getAllVeterinaires',
          );

          // Convertir les données en objets Veterinaire
          final veterinaires =
              paramData
                  .map(
                    (userData) => Veterinaire.fromMap(
                      _modelService.standardizeMap(userData),
                    ),
                  )
                  .toList();

          return veterinaires;
        }

        // Si toujours rien, retourner une liste vide
        return [];
      }

      // Convertir les données en objets Veterinaire
      final veterinaires =
          usersData
              .map(
                (userData) =>
                    Veterinaire.fromMap(_modelService.standardizeMap(userData)),
              )
              .toList();

      // Journaliser le résultat final
      ErrorHandler.instance.logInfo(
        'Conversion réussie: ${veterinaires.length} vétérinaires',
        context: 'UnifiedApiService.getAllVeterinaires',
      );

      return veterinaires;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService.getAllVeterinaires',
      );

      // Essayer une approche de dernier recours
      try {
        ErrorHandler.instance.logInfo(
          'Tentative de dernier recours - récupération directe...',
          context: 'UnifiedApiService.getAllVeterinaires',
        );

        // Essayer avec une URL directe
        final directUrl = '${getBaseUrl()}veterinaires';
        final uri = Uri.parse(directUrl);

        ErrorHandler.instance.logInfo(
          'Requête directe à: $directUrl',
          context: 'UnifiedApiService.getAllVeterinaires',
        );

        final headers = <String, String>{'Content-Type': 'application/json'};
        if (_authToken != null) {
          headers['Authorization'] = 'Bearer $_authToken';
        }

        final response = await _httpClient
            .get(uri, headers: headers)
            .timeout(Duration(seconds: _config.defaultTimeout));

        if (response.statusCode == 200) {
          final jsonResponse = json.decode(response.body);

          final jsonStr = jsonResponse.toString();
          final truncatedJson =
              jsonStr.length > 200
                  ? '${jsonStr.substring(0, 200)}...'
                  : jsonStr;

          ErrorHandler.instance.logInfo(
            'Réponse directe reçue: $truncatedJson',
            context: 'UnifiedApiService.getAllVeterinaires',
          );

          if (jsonResponse is Map && jsonResponse.containsKey('data')) {
            final data = jsonResponse['data'];
            if (data is List) {
              // Filtrer manuellement
              final filteredData =
                  data
                      .where(
                        (item) =>
                            item is Map &&
                            (item['isValidated'] == true ||
                                item['isValid'] == true) &&
                            (item['isBlocked'] == false ||
                                item['isBlocked'] == null),
                      )
                      .toList();

              // Convertir en objets Veterinaire
              final veterinaires =
                  filteredData
                      .map(
                        (userData) => Veterinaire.fromMap(
                          _modelService.standardizeMap(userData),
                        ),
                      )
                      .toList();

              return veterinaires;
            }
          }
        }
      } catch (directError) {
        ErrorHandler.instance.logError(
          directError,
          context: 'UnifiedApiService.getAllVeterinaires (direct)',
        );
      }

      return [];
    }
  }

  /// Récupérer un maryeur par son ID
  Future<Maryeur?> getMaryeur(String id) async {
    try {
      final response = await get('maryeurs/$id');
      final userData = _extractData(response);
      if (userData != null && userData is Map<String, dynamic>) {
        return Maryeur.fromMap(_modelService.standardizeMap(userData));
      }
      return null;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService.getMaryeur',
      );
      return null;
    }
  }

  /// Récupérer tous les maryeurs
  Future<List<Maryeur>> getAllMaryeurs() async {
    try {
      // Journaliser l'appel API
      ErrorHandler.instance.logInfo(
        'Récupération de tous les maryeurs...',
        context: 'UnifiedApiService.getAllMaryeurs',
      );

      // Vérifier d'abord la connectivité au serveur
      final isConnected = await checkServerConnectivity();
      if (!isConnected) {
        ErrorHandler.instance.logWarning(
          'Impossible de se connecter au serveur pour récupérer les maryeurs',
          context: 'UnifiedApiService.getAllMaryeurs',
        );
        throw AppError(
          message:
              'Impossible de se connecter au serveur. Vérifiez que le serveur backend est démarré et que votre appareil est sur le même réseau.',
          type: ErrorType.network,
          context: 'UnifiedApiService.getAllMaryeurs',
        );
      }

      // APPROCHE SIMPLIFIÉE: Récupérer tous les maryeurs sans filtre
      final response = await get('maryeurs');

      // Journaliser la réponse complète pour le débogage (limité à 500 caractères)
      final responseStr = response.toString();
      final truncatedResponse =
          responseStr.length > 500
              ? '${responseStr.substring(0, 500)}...'
              : responseStr;

      ErrorHandler.instance.logInfo(
        'Réponse API mareyeurs: $truncatedResponse',
        context: 'UnifiedApiService.getAllMaryeurs',
      );

      // Extraire les données de la réponse
      final List<dynamic> allUsersData = _extractDataList(response);

      // Filtrer manuellement les mareyeurs validés et non bloqués
      final List<dynamic> usersData =
          allUsersData
              .where(
                (item) =>
                    item is Map &&
                    (item['isValidated'] == true || item['isValid'] == true) &&
                    (item['isBlocked'] == false || item['isBlocked'] == null),
              )
              .toList();

      // Journaliser le nombre de mareyeurs récupérés
      ErrorHandler.instance.logInfo(
        'Nombre total de mareyeurs: ${allUsersData.length}, Nombre de mareyeurs validés et non bloqués: ${usersData.length}',
        context: 'UnifiedApiService.getAllMaryeurs',
      );

      if (usersData.isEmpty) {
        ErrorHandler.instance.logWarning(
          'Aucun mareyeur validé et non bloqué trouvé',
          context: 'UnifiedApiService.getAllMaryeurs',
        );

        // Essayer avec une requête explicite avec paramètres
        ErrorHandler.instance.logInfo(
          'Tentative avec paramètres explicites...',
          context: 'UnifiedApiService.getAllMaryeurs',
        );

        final paramResponse = await get(
          'maryeurs',
          queryParameters: {'isValidated': 'true', 'isBlocked': 'false'},
        );

        final paramData = _extractDataList(paramResponse);

        if (paramData.isNotEmpty) {
          ErrorHandler.instance.logInfo(
            'Récupération avec paramètres réussie: ${paramData.length} mareyeurs trouvés',
            context: 'UnifiedApiService.getAllMaryeurs',
          );

          // Convertir les données en objets Maryeur
          final maryeurs =
              paramData
                  .map(
                    (userData) =>
                        Maryeur.fromMap(_modelService.standardizeMap(userData)),
                  )
                  .toList();

          return maryeurs;
        }

        // Si toujours rien, retourner une liste vide
        return [];
      }

      // Convertir les données en objets Maryeur
      final maryeurs =
          usersData
              .map(
                (userData) =>
                    Maryeur.fromMap(_modelService.standardizeMap(userData)),
              )
              .toList();

      // Journaliser le résultat final
      ErrorHandler.instance.logInfo(
        'Conversion réussie: ${maryeurs.length} mareyeurs',
        context: 'UnifiedApiService.getAllMaryeurs',
      );

      return maryeurs;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService.getAllMaryeurs',
      );

      // Essayer une approche de dernier recours
      try {
        ErrorHandler.instance.logInfo(
          'Tentative de dernier recours - récupération directe...',
          context: 'UnifiedApiService.getAllMaryeurs',
        );

        // Essayer avec une URL directe
        final directUrl = '${getBaseUrl()}maryeurs';
        final uri = Uri.parse(directUrl);

        ErrorHandler.instance.logInfo(
          'Requête directe à: $directUrl',
          context: 'UnifiedApiService.getAllMaryeurs',
        );

        final headers = <String, String>{'Content-Type': 'application/json'};
        if (_authToken != null) {
          headers['Authorization'] = 'Bearer $_authToken';
        }

        final response = await _httpClient
            .get(uri, headers: headers)
            .timeout(Duration(seconds: _config.defaultTimeout));

        if (response.statusCode == 200) {
          final jsonResponse = json.decode(response.body);

          final jsonStr = jsonResponse.toString();
          final truncatedJson =
              jsonStr.length > 200
                  ? '${jsonStr.substring(0, 200)}...'
                  : jsonStr;

          ErrorHandler.instance.logInfo(
            'Réponse directe reçue: $truncatedJson',
            context: 'UnifiedApiService.getAllMaryeurs',
          );

          if (jsonResponse is Map && jsonResponse.containsKey('data')) {
            final data = jsonResponse['data'];
            if (data is List) {
              // Filtrer manuellement
              final filteredData =
                  data
                      .where(
                        (item) =>
                            item is Map &&
                            (item['isValidated'] == true ||
                                item['isValid'] == true) &&
                            (item['isBlocked'] == false ||
                                item['isBlocked'] == null),
                      )
                      .toList();

              // Convertir en objets Maryeur
              final maryeurs =
                  filteredData
                      .map(
                        (userData) => Maryeur.fromMap(
                          _modelService.standardizeMap(userData),
                        ),
                      )
                      .toList();

              return maryeurs;
            }
          }
        }
      } catch (directError) {
        ErrorHandler.instance.logError(
          directError,
          context: 'UnifiedApiService.getAllMaryeurs (direct)',
        );
      }

      return [];
    }
  }

  /// Récupérer un client par son ID
  Future<Client?> getClient(String id) async {
    try {
      final response = await get('clients/$id');
      final userData = _extractData(response);
      if (userData != null && userData is Map<String, dynamic>) {
        return Client.fromMap(_modelService.standardizeMap(userData));
      }
      return null;
    } catch (e) {
      ErrorHandler.instance.logError(e, context: 'UnifiedApiService.getClient');
      return null;
    }
  }

  /// Extraire les données d'une réponse API
  ///
  /// Cette méthode extrait les données d'une réponse API standardisée.
  /// Elle gère les différents formats de réponse possibles.
  dynamic _extractData(Map<String, dynamic> response) {
    // Journaliser la structure de la réponse pour le débogage
    ErrorHandler.instance.logInfo(
      'Structure de la réponse: ${response.keys.join(', ')}',
      context: 'UnifiedApiService._extractData',
    );

    // Cas 1: Structure {"success":true,"message":"...","data":{"data":[...]}}
    if (response.containsKey('data') &&
        response['data'] is Map &&
        (response['data'] as Map).containsKey('data')) {
      return (response['data'] as Map)['data'];
    }
    // Cas 2: Structure {"success":true,"message":"...","data":...}
    else if (response.containsKey('data') && response['data'] != null) {
      return response['data'];
    }
    // Cas 3: Structure {"success":false,"error":"..."}
    else if (response.containsKey('success') && response['success'] == false) {
      // C'est une erreur, retourner null
      ErrorHandler.instance.logWarning(
        'Réponse d\'erreur: ${response['error'] ?? 'Erreur inconnue'}',
        context: 'UnifiedApiService._extractData',
      );
      return null;
    }
    // Cas 4: Structure sans champ 'data' ni 'success'
    else {
      // Exclure les champs spéciaux
      final excludedKeys = ['success', 'message', 'error', 'token', 'status'];
      final dataFields = Map<String, dynamic>.fromEntries(
        response.entries.where((entry) => !excludedKeys.contains(entry.key)),
      );

      // Si des champs ont été trouvés, les retourner
      if (dataFields.isNotEmpty) {
        return dataFields;
      }

      // Sinon, retourner la réponse complète
      return response;
    }
  }

  /// Extraire une liste de données d'une réponse API
  List<dynamic> _extractDataList(Map<String, dynamic> response) {
    // Journaliser la réponse complète pour le débogage (limité à 500 caractères)
    final responseStr = response.toString();
    final truncatedResponse =
        responseStr.length > 500
            ? '${responseStr.substring(0, 500)}...'
            : responseStr;

    ErrorHandler.instance.logInfo(
      'Réponse complète: $truncatedResponse',
      context: 'UnifiedApiService._extractDataList',
    );

    ErrorHandler.instance.logInfo(
      'Structure de la réponse: ${response.keys.join(', ')}',
      context: 'UnifiedApiService._extractDataList',
    );

    // Note: La réponse est toujours un Map<String, dynamic> dans cette méthode
    // donc ce cas ne devrait jamais se produire, mais gardons-le pour la sécurité

    // NOUVEAU: Vérifier si la réponse contient un champ 'docs' (format MongoDB pagination)
    if (response.containsKey('docs') && response['docs'] is List) {
      final result = response['docs'] as List<dynamic>;
      ErrorHandler.instance.logInfo(
        'Extraction via structure docs (pagination MongoDB): ${result.length} éléments trouvés',
        context: 'UnifiedApiService._extractDataList',
      );
      return result;
    }

    // Cas 1: Structure {"success":true,"message":"...","data":{"data":[...]}}
    if (response.containsKey('data') &&
        response['data'] is Map &&
        (response['data'] as Map).containsKey('data') &&
        (response['data'] as Map)['data'] is List) {
      final result = (response['data'] as Map)['data'] as List<dynamic>;
      ErrorHandler.instance.logInfo(
        'Extraction via structure data.data: ${result.length} éléments trouvés',
        context: 'UnifiedApiService._extractDataList',
      );
      return result;
    }
    // Cas 2: Structure {"success":true,"message":"...","data":[...]}
    else if (response.containsKey('data') && response['data'] is List) {
      final result = response['data'] as List<dynamic>;
      ErrorHandler.instance.logInfo(
        'Extraction via structure data (liste): ${result.length} éléments trouvés',
        context: 'UnifiedApiService._extractDataList',
      );
      return result;
    }
    // Cas 3: Structure {"data":{...}} avec des champs qui pourraient contenir une liste
    else if (response.containsKey('data') && response['data'] is Map) {
      final dataMap = response['data'] as Map<String, dynamic>;

      // Journaliser les clés disponibles dans dataMap
      ErrorHandler.instance.logInfo(
        'Clés dans data: ${dataMap.keys.join(', ')}',
        context: 'UnifiedApiService._extractDataList',
      );

      // NOUVEAU: Vérifier si dataMap contient un champ 'docs' (format MongoDB pagination)
      if (dataMap.containsKey('docs') && dataMap['docs'] is List) {
        final result = dataMap['docs'] as List<dynamic>;
        ErrorHandler.instance.logInfo(
          'Extraction via structure data.docs (pagination MongoDB): ${result.length} éléments trouvés',
          context: 'UnifiedApiService._extractDataList',
        );
        return result;
      }

      // Chercher une liste dans les clés de dataMap
      for (final key in dataMap.keys) {
        if (dataMap[key] is List) {
          final result = dataMap[key] as List<dynamic>;
          ErrorHandler.instance.logInfo(
            'Extraction via structure data.$key: ${result.length} éléments trouvés',
            context: 'UnifiedApiService._extractDataList',
          );
          return result;
        }
      }

      // Si aucune liste n'est trouvée mais qu'il y a des éléments, essayer de les convertir en liste
      if (dataMap.isNotEmpty) {
        // Si dataMap contient des objets qui semblent être des utilisateurs, les convertir en liste
        if (dataMap.values.any(
          (value) =>
              value is Map &&
              (value.containsKey('nom') || value.containsKey('email')),
        )) {
          final result =
              dataMap.entries
                  .where((entry) => entry.value is Map)
                  .map((entry) => entry.value as Map<String, dynamic>)
                  .toList();

          ErrorHandler.instance.logInfo(
            'Conversion de map en liste: ${result.length} éléments trouvés',
            context: 'UnifiedApiService._extractDataList',
          );

          return result;
        }
      }
    }

    // Cas 4: Recherche directe dans la réponse (sans passer par 'data')
    // Chercher une liste dans les clés de premier niveau
    for (final key in response.keys) {
      if (response[key] is List) {
        final result = response[key] as List<dynamic>;
        ErrorHandler.instance.logInfo(
          'Extraction via structure $key: ${result.length} éléments trouvés',
          context: 'UnifiedApiService._extractDataList',
        );
        return result;
      }
    }

    // Cas 5: Si la réponse elle-même est une liste d'objets
    if (response.values.any(
      (value) =>
          value is Map &&
          (value.containsKey('nom') || value.containsKey('email')),
    )) {
      final result =
          response.entries
              .where((entry) => entry.value is Map)
              .map((entry) => entry.value as Map<String, dynamic>)
              .toList();

      ErrorHandler.instance.logInfo(
        'Extraction d\'objets utilisateurs: ${result.length} éléments trouvés',
        context: 'UnifiedApiService._extractDataList',
      );

      return result;
    }

    // NOUVEAU: Dernier recours - si la réponse contient un champ qui ressemble à un tableau d'objets
    // Parcourir toutes les valeurs et chercher des tableaux
    for (final value in response.values) {
      if (value is List && value.isNotEmpty && value.first is Map) {
        ErrorHandler.instance.logInfo(
          'Extraction de dernier recours - tableau trouvé: ${value.length} éléments',
          context: 'UnifiedApiService._extractDataList',
        );
        return value;
      }
    }

    // Aucune structure reconnue, retourner une liste vide
    ErrorHandler.instance.logWarning(
      'Aucune structure de données reconnue dans la réponse',
      context: 'UnifiedApiService._extractDataList',
    );
    return [];
  }

  /// Récupérer les achats d'un client
  Future<List<Lot>> getMyPurchases(String clientId) async {
    try {
      final response = await get('clients/$clientId/purchases');
      if (response.containsKey('data') && response['data'] is List) {
        final List<dynamic> lotsData = response['data'];
        return lotsData
            .map(
              (lotData) => Lot.fromMap(_modelService.standardizeMap(lotData)),
            )
            .toList();
      }
      return [];
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService.getMyPurchases',
      );
      return [];
    }
  }

  /// Récupérer tous les clients
  Future<List<Client>> getAllClients() async {
    try {
      final response = await get('clients');
      if (response.containsKey('data') && response['data'] is List) {
        final List<dynamic> usersData = response['data'];
        return usersData
            .map(
              (userData) =>
                  Client.fromMap(_modelService.standardizeMap(userData)),
            )
            .toList();
      }
      return [];
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService.getAllClients',
      );
      return [];
    }
  }

  //
  // Méthodes spécifiques pour les lots
  //

  /// Récupérer un lot par son ID
  Future<Lot?> getLot(String id) async {
    try {
      final response = await get('lots/$id');
      if (response.containsKey('data') && response['data'] != null) {
        final lotData = response['data'];
        return Lot.fromMap(_modelService.standardizeMap(lotData));
      }
      return null;
    } catch (e) {
      ErrorHandler.instance.logError(e, context: 'UnifiedApiService.getLot');
      return null;
    }
  }

  /// Récupérer les lots en attente pour un vétérinaire
  Future<List<Lot>> getPendingLots() async {
    try {
      final response = await get('lots/pending');
      if (response.containsKey('data') && response['data'] is List) {
        final List<dynamic> lotsData = response['data'];
        return lotsData
            .map(
              (lotData) => Lot.fromMap(_modelService.standardizeMap(lotData)),
            )
            .toList();
      }
      return [];
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService.getPendingLots',
      );
      return [];
    }
  }

  /// Récupérer les lots d'un pêcheur
  Future<List<Lot>> getLotsByPecheurId(String pecheurId) async {
    try {
      final response = await get('lots/pecheur/$pecheurId');
      if (response.containsKey('data') && response['data'] is List) {
        final List<dynamic> lotsData = response['data'];
        return lotsData
            .map(
              (lotData) => Lot.fromMap(_modelService.standardizeMap(lotData)),
            )
            .toList();
      }
      return [];
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService.getLotsByPecheurId',
      );
      return [];
    }
  }

  /// Approuver un lot
  Future<bool> approveLot(String lotId, String veterinaireId) async {
    try {
      // Journaliser l'appel pour le débogage
      ErrorHandler.instance.logInfo(
        'Approbation du lot $lotId par le vétérinaire $veterinaireId',
        context: 'UnifiedApiService.approveLot',
      );

      final response = await put('lots/$lotId/approve', {
        'temperature': 4.0, // Température par défaut
        'veterinaire': veterinaireId,
      });

      // Journaliser la réponse
      ErrorHandler.instance.logInfo(
        'Réponse de l\'approbation du lot: $response',
        context: 'UnifiedApiService.approveLot',
      );

      return true;
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService.approveLot',
      );
      return false;
    }
  }

  /// Rejeter un lot
  Future<bool> rejectLot(
    String lotId,
    String veterinaireId, {
    String? motif,
  }) async {
    try {
      // Journaliser l'appel pour le débogage
      ErrorHandler.instance.logInfo(
        'Rejet du lot $lotId par le vétérinaire $veterinaireId',
        context: 'UnifiedApiService.rejectLot',
      );

      final response = await put('lots/$lotId/reject', {
        'veterinaire': veterinaireId,
        if (motif != null) 'motif': motif,
      });

      // Journaliser la réponse
      ErrorHandler.instance.logInfo(
        'Réponse du rejet du lot: $response',
        context: 'UnifiedApiService.rejectLot',
      );

      return true;
    } catch (e) {
      ErrorHandler.instance.logError(e, context: 'UnifiedApiService.rejectLot');
      return false;
    }
  }

  /// Récupérer les enchères actives pour un maryeur
  Future<List<Lot>> getActiveAuctionsByMaryeurId(String maryeurId) async {
    try {
      final response = await get('lots/maryeur/$maryeurId/active');
      if (response.containsKey('data') && response['data'] is List) {
        final List<dynamic> lotsData = response['data'];
        return lotsData
            .map(
              (lotData) => Lot.fromMap(_modelService.standardizeMap(lotData)),
            )
            .toList();
      }
      return [];
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService.getActiveAuctionsByMaryeurId',
      );
      return [];
    }
  }

  /// Récupérer les enchères disponibles pour un client
  Future<List<Lot>> getAvailableAuctions() async {
    try {
      final response = await get('lots/auctions/available');
      if (response.containsKey('data') && response['data'] is List) {
        final List<dynamic> lotsData = response['data'];
        return lotsData
            .map(
              (lotData) => Lot.fromMap(_modelService.standardizeMap(lotData)),
            )
            .toList();
      }
      return [];
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'UnifiedApiService.getAvailableAuctions',
      );
      return [];
    }
  }

  /// Placer une enchère
  Future<bool> placeBid(
    String auctionId,
    double bidAmount,
    String clientId,
  ) async {
    try {
      await post('lots/$auctionId/bid', {
        'amount': bidAmount,
        'client': clientId,
      });
      return true;
    } catch (e) {
      ErrorHandler.instance.logError(e, context: 'UnifiedApiService.placeBid');
      return false;
    }
  }
}
