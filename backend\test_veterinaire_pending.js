const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3005/api';

// Données de test pour un vétérinaire
const testVeterinaire = {
  email: '<EMAIL>',
  password: 'Insaf12*'
};

// Fonction pour se connecter en tant que vétérinaire
async function loginVeterinaire() {
  try {
    console.log('🔐 Connexion du vétérinaire...');
    const response = await axios.post(`${BASE_URL}/auth/login`, testVeterinaire);
    
    if (response.data && response.data.token) {
      console.log('✅ Connexion réussie');
      console.log(`Vétérinaire: ${response.data.user.prenom} ${response.data.user.nom}`);
      console.log(`Rôles: ${response.data.user.roles}`);
      console.log(`ID: ${response.data.user._id}`);
      return {
        token: response.data.token,
        user: response.data.user
      };
    } else {
      throw new Error('Pas de token dans la réponse');
    }
  } catch (error) {
    console.error('❌ Erreur de connexion:', error.response?.data || error.message);
    throw error;
  }
}

// Fonction pour tester l'endpoint /lots/pending
async function testPendingLots(token, user) {
  try {
    console.log('\n📦 Test de l\'endpoint /lots/pending...');
    console.log(`Vétérinaire connecté: ${user.prenom} ${user.nom} (ID: ${user._id})`);
    
    const response = await axios.get(`${BASE_URL}/lots/pending`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('\n📋 Réponse de l\'API:');
    console.log(`- Status: ${response.status}`);
    console.log(`- Success: ${response.data.success}`);
    console.log(`- Message: ${response.data.message}`);
    console.log(`- Data type: ${typeof response.data.data}`);
    
    if (response.data.data) {
      console.log(`- Nombre de lots: ${response.data.data.length || 'N/A'}`);
      
      if (Array.isArray(response.data.data) && response.data.data.length > 0) {
        console.log('\n🔍 Premier lot:');
        const firstLot = response.data.data[0];
        console.log(`  - ID: ${firstLot._id}`);
        console.log(`  - Identifiant: ${firstLot.identifiant}`);
        console.log(`  - Espèce: ${firstLot.especeNom}`);
        console.log(`  - Vétérinaire assigné: ${firstLot.veterinaire}`);
        console.log(`  - Test: ${firstLot.test}`);
        console.log(`  - Status: ${firstLot.status}`);
        console.log(`  - isValidated: ${firstLot.isValidated}`);
        console.log(`  - Date soumission: ${firstLot.dateSoumission}`);
      } else {
        console.log('📝 Aucun lot en attente trouvé');
      }
    }
    
    return response.data;
  } catch (error) {
    console.error('❌ Erreur lors du test /lots/pending:');
    console.error(`- Status: ${error.response?.status}`);
    console.error(`- Message: ${error.response?.data?.message || error.message}`);
    console.error(`- Data:`, error.response?.data);
    throw error;
  }
}

// Fonction pour créer un lot de test (pour avoir quelque chose en attente)
async function createTestLot() {
  try {
    console.log('\n📦 Création d\'un lot de test...');
    
    // Se connecter en tant que pêcheur
    const pecheurLogin = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'Zouhaier1*'
    });
    
    const pecheurToken = pecheurLogin.data.token;
    const pecheurUser = pecheurLogin.data.user;
    
    console.log(`Pêcheur connecté: ${pecheurUser.prenom} ${pecheurUser.nom}`);
    
    // Récupérer les vétérinaires et mareyeurs
    const vetResponse = await axios.get(`${BASE_URL}/veterinaires`, {
      headers: { Authorization: `Bearer ${pecheurToken}` }
    });
    
    const marResponse = await axios.get(`${BASE_URL}/maryeurs`, {
      headers: { Authorization: `Bearer ${pecheurToken}` }
    });
    
    const veterinaires = vetResponse.data.data?.data || vetResponse.data.data || [];
    const mareyeurs = marResponse.data.data?.data || marResponse.data.data || [];
    
    if (veterinaires.length === 0 || mareyeurs.length === 0) {
      throw new Error('Pas assez de vétérinaires ou mareyeurs');
    }
    
    // Convertir les IDs buffer en string
    function convertBufferId(id) {
      if (typeof id === 'string') return id;
      if (id && id.buffer) {
        const bytes = [];
        for (let i = 0; i < 12; i++) {
          bytes.push(id.buffer[i.toString()]);
        }
        return bytes.map(b => b.toString(16).padStart(2, '0')).join('');
      }
      return id.toString();
    }
    
    const vetId = convertBufferId(veterinaires[0].id);
    const marId = convertBufferId(mareyeurs[0].id);
    
    console.log(`Vétérinaire sélectionné: ${veterinaires[0].prenom} ${veterinaires[0].nom} (ID: ${vetId})`);
    console.log(`Mareyeur sélectionné: ${mareyeurs[0].prenom} ${mareyeurs[0].nom} (ID: ${marId})`);
    
    // Créer le lot
    const lotData = {
      identifiant: `LOT-VET-TEST-${Date.now()}`,
      nom: 'Lot de test pour vétérinaire',
      photo: 'test-vet.jpg',
      poids: 1.5,
      temperature: 4,
      quantite: 1,
      especeNom: 'Dorade',
      engin: 'Filet',
      zone: 'Zone test vétérinaire',
      lieu: 'Sfax',
      veterinaire: vetId,
      maryeur: marId,
      status: false,
      test: false,
      vendu: false,
      isValidated: false
    };
    
    const lotResponse = await axios.post(`${BASE_URL}/lots`, lotData, {
      headers: { 
        Authorization: `Bearer ${pecheurToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Lot de test créé avec succès!');
    console.log(`- ID: ${lotResponse.data.lot?.id || 'N/A'}`);
    console.log(`- Identifiant: ${lotResponse.data.lot?.identifiant || 'N/A'}`);
    
    return lotResponse.data;
  } catch (error) {
    console.error('❌ Erreur lors de la création du lot de test:', error.response?.data || error.message);
    // Ne pas faire échouer le test principal si la création du lot échoue
    return null;
  }
}

// Fonction principale
async function main() {
  try {
    console.log('🚀 Test de l\'endpoint /lots/pending pour vétérinaire\n');
    
    // 1. Créer un lot de test (optionnel)
    await createTestLot();
    
    // 2. Se connecter en tant que vétérinaire
    const { token, user } = await loginVeterinaire();
    
    // 3. Tester l'endpoint /lots/pending
    await testPendingLots(token, user);
    
    console.log('\n🎉 Test terminé avec succès!');
    
  } catch (error) {
    console.error('\n💥 Test échoué:', error.message);
    process.exit(1);
  }
}

// Exécuter le test
main();
