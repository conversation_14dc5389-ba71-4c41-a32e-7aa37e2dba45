// Test simple pour vérifier si le serveur peut démarrer
console.log('🚀 Test de démarrage du serveur...');

try {
  // Test des imports de base
  console.log('📦 Test des imports...');
  
  const express = require('express');
  console.log('✅ Express importé');
  
  const mongoose = require('mongoose');
  console.log('✅ Mongoose importé');
  
  const cors = require('cors');
  console.log('✅ CORS importé');
  
  // Test de création d'une app Express simple
  console.log('🔧 Création de l\'application Express...');
  const app = express();
  console.log('✅ Application Express créée');
  
  // Test de middleware de base
  app.use(cors());
  app.use(express.json());
  console.log('✅ Middlewares de base ajoutés');
  
  // Route de test simple
  app.get('/test', (req, res) => {
    res.json({ message: 'Serveur de test fonctionnel', timestamp: new Date().toISOString() });
  });
  console.log('✅ Route de test ajoutée');
  
  // Test de démarrage du serveur
  const PORT = 3006; // Port différent pour éviter les conflits
  const server = app.listen(PORT, '0.0.0.0', () => {
    console.log(`✅ Serveur de test démarré sur http://0.0.0.0:${PORT}`);
    console.log('🎉 Test de démarrage réussi !');
    
    // Arrêter le serveur après 5 secondes
    setTimeout(() => {
      console.log('🛑 Arrêt du serveur de test...');
      server.close(() => {
        console.log('✅ Serveur de test arrêté');
        process.exit(0);
      });
    }, 5000);
  });
  
  server.on('error', (error) => {
    console.error('❌ Erreur du serveur de test:', error);
    process.exit(1);
  });
  
} catch (error) {
  console.error('❌ Erreur lors du test de démarrage:', error);
  console.error('Stack trace:', error.stack);
  process.exit(1);
}
