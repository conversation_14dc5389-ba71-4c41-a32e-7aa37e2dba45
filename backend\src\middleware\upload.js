const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Assurez-vous que le dossier uploads existe
const uploadDir = path.join(__dirname, '../../uploads');
try {
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
    fs.chmodSync(uploadDir, 0o755);
  }
} catch (error) {
  console.error('Erreur lors de la création du dossier d\'upload:', error);
}

// Configuration du stockage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Vérifier que le dossier existe au moment de l'upload
    if (!fs.existsSync(uploadDir)) {
      try {
        fs.mkdirSync(uploadDir, { recursive: true });
      } catch (mkdirError) {
        return cb(new Error(`Impossible de créer le dossier d'upload: ${mkdirError.message}`), null);
      }
    }

    // Vérifier les permissions d'écriture
    try {
      fs.accessSync(uploadDir, fs.constants.W_OK);
      console.log(`Permissions d'écriture OK sur le dossier: ${uploadDir}`);
      cb(null, uploadDir);
    } catch (error) {
      console.error(`Erreur de permission sur le dossier d'upload:`, error);

      // Tenter de corriger les permissions
      try {
        fs.chmodSync(uploadDir, 0o755);
        console.log(`Permissions du dossier mises à jour à 0755`);
        cb(null, uploadDir);
      } catch (chmodError) {
        console.error(`Impossible de modifier les permissions:`, chmodError);
        cb(new Error(`Pas de permission d'écriture sur le dossier d'upload: ${error.message}`), null);
      }
    }
  },
  filename: (req, file, cb) => {
    try {
      // Nettoyer le nom de fichier original
      let originalName = path.basename(file.originalname).replace(/[^a-zA-Z0-9.]/g, '_');
      console.log(`Nom de fichier original nettoyé: ${originalName}`);

      // Générer un suffixe unique
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);

      // Extension d'origine
      let ext = path.extname(originalName).toLowerCase();
      console.log(`Extension détectée avant correction: ${ext}`);

      // Corriger l'extension si le MIME est image/heic et extension différente
      if (file.mimetype === 'image/heic' && ext !== '.heic') {
        ext = '.heic';
        console.log(`Extension corrigée à .heic car MIME est image/heic`);
      }

      // Liste des extensions valides (ajout de .heic)
      const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.heic'];
      if (!validExtensions.includes(ext)) {
        console.error(`Extension de fichier non supportée: ${ext}`);
        return cb(new Error(`Extension de fichier non supportée: ${ext}. Utilisez JPG, PNG, GIF, WebP ou HEIC.`), null);
      }

      // Liste des types MIME valides (ajout de image/heic)
      const validMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/heic'];
      if (!validMimeTypes.includes(file.mimetype)) {
        console.error(`Type MIME non supporté: ${file.mimetype}`);
        return cb(new Error(`Type de fichier non supporté: ${file.mimetype}. Utilisez JPG, PNG, GIF, WebP ou HEIC.`), null);
      }

      const filename = `profile_${uniqueSuffix}${ext}`;
      console.log(`Nom de fichier généré: ${filename}`);
      cb(null, filename);
    } catch (error) {
      console.error('Erreur lors de la génération du nom de fichier:', error);
      cb(error, null);
    }
  }
});

// Filtre pour n'accepter que les images
const fileFilter = (req, file, cb) => {
  const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/heic'];

  if (allowedMimeTypes.includes(file.mimetype)) {
    console.log(`Type MIME accepté: ${file.mimetype}`);
    cb(null, true);
  } else {
    console.error(`Type MIME rejeté: ${file.mimetype}`);
    cb(new Error(`Type de fichier non supporté: ${file.mimetype}. Utilisez JPG, PNG, GIF, WebP ou HEIC.`), false);
  }
};



// Gestion des erreurs de multer
const handleMulterError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    // Erreur Multer spécifique
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        error: 'La taille du fichier dépasse la limite de 5 MB'
      });
    }
    return res.status(400).json({ error: `Erreur d'upload: ${err.message}` });
  } else if (err) {
    // Autre erreur
    return res.status(500).json({ error: err.message });
  }
  next();
};

// Configuration de multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // Limite à 5MB
  }
});

// Exporter à la fois le middleware d'upload et le gestionnaire d'erreurs
module.exports = upload;
