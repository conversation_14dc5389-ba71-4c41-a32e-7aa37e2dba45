import 'dart:io';
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:seatrace/models/lot.dart';
import 'package:seatrace/screens/maryeur/extend_auction_dialog.dart';
import 'package:seatrace/services/unified_lot_service.dart';
import 'package:seatrace/utils/error_handler.dart';
import 'package:seatrace/widgets/countdown_timer.dart';
import 'package:seatrace/widgets/custom_app_bar.dart';
import 'package:seatrace/widgets/custom_button.dart';
import 'package:seatrace/widgets/loading_indicator.dart';

/// Écran de détail d'une enchère pour le mareyeur
class MaryeurAuctionDetailScreen extends StatefulWidget {
  final String lotId;

  const MaryeurAuctionDetailScreen({super.key, required this.lotId});

  @override
  State<MaryeurAuctionDetailScreen> createState() =>
      _MaryeurAuctionDetailScreenState();
}

class _MaryeurAuctionDetailScreenState
    extends State<MaryeurAuctionDetailScreen> {
  bool _isLoading = true;
  String? _errorMessage;
  Lot? _lot;
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();
    _loadAuctionDetails();

    // Rafraîchir les détails toutes les 10 secondes
    _refreshTimer = Timer.periodic(const Duration(seconds: 10), (_) {
      _loadAuctionDetails(silent: true);
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  /// Charge les détails de l'enchère
  Future<void> _loadAuctionDetails({bool silent = false}) async {
    if (!silent) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }

    try {
      final lot = await UnifiedLotService().getLotById(widget.lotId);

      if (!silent) {
        setState(() {
          _lot = lot;
          _isLoading = false;
        });
      } else if (mounted) {
        setState(() {
          _lot = lot;
        });
      }
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'MaryeurAuctionDetailScreen._loadAuctionDetails',
      );

      if (!silent && mounted) {
        setState(() {
          _errorMessage =
              'Erreur lors du chargement des détails: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  /// Affiche la boîte de dialogue de confirmation pour clôturer l'enchère
  void _showEndAuctionDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Clôturer l\'enchère'),
            content: const Text(
              'Êtes-vous sûr de vouloir clôturer cette enchère ? Cette action est irréversible.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _endAuction();
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Clôturer'),
              ),
            ],
          ),
    );
  }

  /// Clôture l'enchère
  Future<void> _endAuction() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await UnifiedLotService().endAuction(widget.lotId);

      if (result) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Enchère clôturée avec succès'),
              backgroundColor: Colors.green,
            ),
          );

          // Rafraîchir les détails
          await _loadAuctionDetails();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Erreur lors de la clôture de l\'enchère'),
              backgroundColor: Colors.red,
            ),
          );
          setState(() {
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'MaryeurAuctionDetailScreen._endAuction',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Affiche la boîte de dialogue pour prolonger l'enchère
  void _showExtendAuctionDialog() {
    if (_lot == null) return;

    showDialog(
      context: context,
      builder:
          (context) => ExtendAuctionDialog(
            lot: _lot!,
            onSuccess: () => _loadAuctionDetails(),
          ),
    );
  }

  /// Construit un élément de détail
  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final isAuctionActive =
        (_lot?.enchereActive == true || _lot?.isAuctionActive == true) &&
        (_lot?.dateFinEnchere?.isAfter(now) ?? false);

    return Scaffold(
      appBar: CustomAppBar(title: 'Détail de l\'enchère', showBackButton: true),
      body:
          _isLoading
              ? const Center(child: LoadingIndicator())
              : _errorMessage != null
              ? Center(child: Text(_errorMessage!))
              : _lot == null
              ? const Center(child: Text('Aucune information disponible'))
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Image
                    if (_lot!.photo != null && File(_lot!.photo!).existsSync())
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.file(
                          File(_lot!.photo!),
                          width: double.infinity,
                          height: 250,
                          fit: BoxFit.cover,
                        ),
                      )
                    else
                      Container(
                        width: double.infinity,
                        height: 250,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.image_not_supported,
                          size: 80,
                          color: Colors.grey[500],
                        ),
                      ),
                    const SizedBox(height: 24),

                    // Statut de l'enchère
                    Card(
                      color:
                          isAuctionActive ? Colors.green[50] : Colors.red[50],
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  isAuctionActive
                                      ? Icons.gavel
                                      : Icons.gavel_outlined,
                                  color:
                                      isAuctionActive
                                          ? Colors.green
                                          : Colors.red,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  isAuctionActive
                                      ? 'Enchère en cours'
                                      : 'Enchère terminée',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color:
                                        isAuctionActive
                                            ? Colors.green
                                            : Colors.red,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            if (isAuctionActive && _lot!.dateFinEnchere != null)
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Fin prévue: ${DateFormat('dd/MM/yyyy HH:mm').format(_lot!.dateFinEnchere!)}',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Row(
                                    children: [
                                      const Text(
                                        'Temps restant: ',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      CountdownTimer(
                                        endTime: _lot!.dateFinEnchere!,
                                        textStyle: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                        warningColor: Colors.orange,
                                        expiredColor: Colors.red,
                                        onFinished: () {
                                          // Rafraîchir les détails lorsque le compte à rebours est terminé
                                          if (mounted) {
                                            _loadAuctionDetails();
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            if (_lot!.prixActuel != null)
                              Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Text(
                                  'Enchère actuelle: ${_lot!.prixActuel} dinars',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 18,
                                  ),
                                ),
                              ),
                            if (_lot!.acheteurId != null)
                              Padding(
                                padding: const EdgeInsets.only(top: 4.0),
                                child: Text(
                                  'Meilleur enchérisseur: ${_lot!.acheteurId}',
                                  style: const TextStyle(
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Actions
                    if (isAuctionActive)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Expanded(
                            child: CustomButton(
                              text: 'Prolonger',
                              onPressed: _showExtendAuctionDialog,
                              icon: Icons.add_alarm,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: CustomButton(
                              text: 'Clôturer',
                              onPressed: _showEndAuctionDialog,
                              icon: Icons.stop_circle,
                              color: Colors.red,
                            ),
                          ),
                        ],
                      ),
                    const SizedBox(height: 24),

                    // Détails du lot
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Détails du lot',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 16),
                            _buildDetailItem('Identifiant', _lot!.identifiant),
                            _buildDetailItem('Espèce', _lot!.especeNom),
                            _buildDetailItem(
                              'Quantité',
                              _lot!.quantite.toString(),
                            ),
                            _buildDetailItem('Poids', '${_lot!.poids} kg'),
                            _buildDetailItem(
                              'Prix minimal',
                              '${_lot!.prixMinimal ?? 'N/A'} dinars',
                            ),
                            _buildDetailItem(
                              'Prix initial',
                              '${_lot!.prixInitial ?? 'N/A'} dinars',
                            ),
                            if (_lot!.dateTest != null)
                              _buildDetailItem(
                                'Date de test',
                                DateFormat('dd/MM/yyyy').format(
                                  _lot!.dateTest is DateTime
                                      ? _lot!.dateTest as DateTime
                                      : DateTime.parse(
                                        _lot!.dateTest.toString(),
                                      ),
                                ),
                              ),
                            _buildDetailItem(
                              'Température',
                              '${_lot!.temperature}°C',
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }
}
