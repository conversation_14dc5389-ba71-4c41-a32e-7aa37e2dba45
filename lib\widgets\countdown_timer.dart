import 'dart:async';

import 'package:flutter/material.dart';

/// Widget de compte à rebours pour afficher le temps restant
class CountdownTimer extends StatefulWidget {
  /// Date de fin du compte à rebours
  final DateTime endTime;

  /// Fonction appelée lorsque le compte à rebours est terminé
  final VoidCallback? onFinished;

  /// Style du texte pour le compte à rebours
  final TextStyle? textStyle;

  /// Couleur du texte lorsque le temps restant est faible (< 1 minute)
  final Color? warningColor;

  /// Couleur du texte lorsque le temps est écoulé
  final Color? expiredColor;

  /// Texte à afficher lorsque le temps est écoulé
  final String expiredText;

  /// Afficher les jours dans le compte à rebours
  final bool showDays;

  const CountdownTimer({
    super.key,
    required this.endTime,
    this.onFinished,
    this.textStyle,
    this.warningColor,
    this.expiredColor,
    this.expiredText = 'Terminé',
    this.showDays = false,
  });

  @override
  State<CountdownTimer> createState() => _CountdownTimerState();
}

class _CountdownTimerState extends State<CountdownTimer> {
  late Timer _timer;
  late DateTime _endTime;
  Duration _timeRemaining = Duration.zero;
  bool _isFinished = false;

  @override
  void initState() {
    super.initState();
    _endTime = widget.endTime;
    _calculateTimeRemaining();
    _startTimer();
  }

  @override
  void didUpdateWidget(CountdownTimer oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Si la date de fin a changé, mettre à jour le compte à rebours
    if (widget.endTime != oldWidget.endTime) {
      _endTime = widget.endTime;
      _calculateTimeRemaining();

      // Réinitialiser l'état de fin si la nouvelle date est dans le futur
      if (_timeRemaining.inSeconds > 0 && _isFinished) {
        setState(() {
          _isFinished = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  /// Calcule le temps restant entre maintenant et la date de fin
  void _calculateTimeRemaining() {
    final now = DateTime.now();
    if (_endTime.isAfter(now)) {
      _timeRemaining = _endTime.difference(now);
    } else {
      _timeRemaining = Duration.zero;
      if (!_isFinished) {
        _isFinished = true;
        widget.onFinished?.call();
      }
    }
  }

  /// Démarre le timer qui met à jour le compte à rebours chaque seconde
  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _calculateTimeRemaining();
        });
      }
    });
  }

  /// Formate la durée en chaîne de caractères lisible
  String _formatDuration(Duration duration) {
    if (duration.inSeconds <= 0) {
      return widget.expiredText;
    }

    final days = duration.inDays;
    final hours = duration.inHours.remainder(24);
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (widget.showDays && days > 0) {
      return '${days}j ${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      final totalHours = duration.inHours;
      return '${totalHours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  @override
  Widget build(BuildContext context) {
    final defaultTextStyle = Theme.of(
      context,
    ).textTheme.titleMedium!.copyWith(fontWeight: FontWeight.bold);

    final textStyle = widget.textStyle ?? defaultTextStyle;

    // Déterminer la couleur du texte en fonction du temps restant
    Color textColor = textStyle.color ?? Theme.of(context).colorScheme.primary;

    if (_timeRemaining.inSeconds <= 0) {
      // Temps écoulé
      textColor = widget.expiredColor ?? Colors.red;
    } else if (_timeRemaining.inMinutes < 1) {
      // Moins d'une minute restante
      textColor = widget.warningColor ?? Colors.orange;
    }

    return Text(
      _formatDuration(_timeRemaining),
      style: textStyle.copyWith(color: textColor),
    );
  }
}
