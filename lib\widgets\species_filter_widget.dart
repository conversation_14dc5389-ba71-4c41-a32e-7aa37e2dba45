import 'package:flutter/material.dart';

/// Widget de filtre par espèce de poisson
class SpeciesFilterWidget extends StatefulWidget {
  final String? selectedSpecies;
  final Function(String?) onSpeciesChanged;
  final List<String> availableSpecies;
  final bool showAllOption;
  final String allOptionText;

  const SpeciesFilterWidget({
    super.key,
    this.selectedSpecies,
    required this.onSpeciesChanged,
    this.availableSpecies = const [],
    this.showAllOption = true,
    this.allOptionText = 'Toutes les espèces',
  });

  @override
  State<SpeciesFilterWidget> createState() => _SpeciesFilterWidgetState();
}

class _SpeciesFilterWidgetState extends State<SpeciesFilterWidget> {
  // Liste des espèces communes dans la région méditerranéenne tunisienne
  static const List<String> _commonSpecies = [
    'Rouget',
    'Daurade',
    'Loup de mer',
    'Sardine',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>d',
  ];

  List<String> get _allSpecies {
    final Set<String> speciesSet = <String>{};

    // Ajouter les espèces disponibles depuis les données
    speciesSet.addAll(widget.availableSpecies);

    // Ajouter les espèces communes
    speciesSet.addAll(_commonSpecies);

    // Convertir en liste triée
    final List<String> sortedSpecies = speciesSet.toList()..sort();

    return sortedSpecies;
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = const Color(0xFF1565C0);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String?>(
          value: widget.selectedSpecies,
          hint: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.filter_list, size: 16, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              Text(
                'Espèce',
                style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
              ),
            ],
          ),
          icon: Icon(Icons.arrow_drop_down, color: Colors.grey.shade600),
          isExpanded: false,
          style: TextStyle(
            color: primaryColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          onChanged: widget.onSpeciesChanged,
          items: [
            // Option "Toutes les espèces" si activée
            if (widget.showAllOption)
              DropdownMenuItem<String?>(
                value: null,
                child: Row(
                  children: [
                    Icon(
                      Icons.all_inclusive,
                      size: 16,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      widget.allOptionText,
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

            // Séparateur si option "Toutes" présente
            if (widget.showAllOption && _allSpecies.isNotEmpty)
              const DropdownMenuItem<String?>(
                enabled: false,
                value: null,
                child: Divider(height: 1),
              ),

            // Options d'espèces
            ..._allSpecies.map((species) {
              return DropdownMenuItem<String?>(
                value: species,
                child: Row(
                  children: [
                    Icon(
                      _getSpeciesIcon(species),
                      size: 16,
                      color: primaryColor,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        species,
                        style: TextStyle(
                          color: Colors.grey.shade800,
                          fontWeight:
                              widget.selectedSpecies == species
                                  ? FontWeight.w600
                                  : FontWeight.normal,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    // Indicateur de sélection
                    if (widget.selectedSpecies == species)
                      Icon(Icons.check, size: 16, color: primaryColor),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  /// Retourne une icône appropriée pour chaque espèce
  IconData _getSpeciesIcon(String species) {
    switch (species.toLowerCase()) {
      case 'rouget':
      case 'daurade':
      case 'loup de mer':
      case 'sole':
      case 'turbot':
      case 'saint-pierre':
      case 'pageot':
      case 'sar':
      case 'merlan':
        return Icons.set_meal; // Poissons nobles
      case 'sardine':
      case 'anchois':
      case 'maquereau':
        return Icons.waves; // Poissons pélagiques
      case 'thon':
        return Icons.speed; // Grands pélagiques
      case 'congre':
        return Icons.timeline; // Poissons longs
      case 'poulpe':
      case 'seiche':
      case 'calamar':
        return Icons.psychology; // Céphalopodes
      case 'crevette':
      case 'langoustine':
      case 'homard':
        return Icons.bug_report; // Crustacés
      default:
        return Icons.phishing; // Icône générique poisson
    }
  }
}

/// Widget compact pour afficher le filtre d'espèce sélectionné
class SelectedSpeciesChip extends StatelessWidget {
  final String species;
  final VoidCallback onRemove;

  const SelectedSpeciesChip({
    super.key,
    required this.species,
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    final primaryColor = const Color(0xFF1565C0);

    return Chip(
      avatar: Icon(Icons.phishing, size: 16, color: primaryColor),
      label: Text(
        species,
        style: TextStyle(
          color: primaryColor,
          fontWeight: FontWeight.w500,
          fontSize: 12,
        ),
      ),
      deleteIcon: Icon(Icons.close, size: 16, color: primaryColor),
      onDeleted: onRemove,
      backgroundColor: primaryColor.withValues(alpha: 0.1),
      side: BorderSide(color: primaryColor.withValues(alpha: 0.3)),
    );
  }
}

/// Extension pour obtenir les espèces disponibles depuis une liste de lots
extension SpeciesExtractor on List<Map<String, dynamic>> {
  List<String> extractAvailableSpecies() {
    final Set<String> species = <String>{};

    for (final lot in this) {
      final especeNom = lot['especeNom'] as String?;
      if (especeNom != null && especeNom.isNotEmpty && especeNom != 'Poisson') {
        species.add(especeNom);
      }
    }

    return species.toList()..sort();
  }
}
