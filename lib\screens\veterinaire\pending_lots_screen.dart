import 'package:flutter/material.dart';
import 'package:seatrace/services/unified_api_service.dart';
import 'package:seatrace/services/unified_auth_service.dart';
import 'package:seatrace/services/unified_lot_service.dart';
import 'package:seatrace/models/veterinaire.dart';
import 'package:seatrace/utils/error_handler.dart';
import 'package:intl/intl.dart';

class PendingLotsScreen extends StatefulWidget {
  const PendingLotsScreen({super.key});

  @override
  State<PendingLotsScreen> createState() => _PendingLotsScreenState();
}

class _PendingLotsScreenState extends State<PendingLotsScreen> {
  List<Map<String, dynamic>> _pendingLots = [];
  List<Map<String, dynamic>> _filteredLots = [];
  bool _isLoading = true;
  String? _errorMessage;
  String? _selectedSpecies;
  List<String> _availableSpecies = [];

  @override
  void initState() {
    super.initState();
    _loadPendingLots();
  }

  Future<void> _loadPendingLots() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Vérifier l'utilisateur avec le service unifié
      final user = await UnifiedAuthService().getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non autorisé');
      }

      // Vérifier que l'utilisateur est bien un vétérinaire
      if (user is! Veterinaire) {
        throw Exception('L\'utilisateur n\'est pas un vétérinaire');
      }

      // Ajouter un log pour le débogage
      ErrorHandler.instance.logInfo(
        'Chargement des lots en attente pour le vétérinaire: ${user.id}',
        context: 'PendingLotsScreen',
      );

      try {
        // Utiliser le service unifié pour récupérer les lots en attente
        // Le backend filtrera automatiquement les lots pour ce vétérinaire spécifique
        final response = await UnifiedApiService().get('lots/pending');

        // Vérifier si la réponse contient des données
        if (response.containsKey('data')) {
          // Ajouter un log pour voir le type de données reçu
          ErrorHandler.instance.logInfo(
            'Type de données reçu: ${response['data'].runtimeType}',
            context: 'PendingLotsScreen',
          );

          // Traiter la réponse en fonction de son type
          if (response['data'] is List) {
            // Filtrer les lots pour ne garder que ceux qui ont des données valides
            final rawLots = List<Map<String, dynamic>>.from(response['data']);
            _pendingLots =
                rawLots
                    .where(
                      (lot) =>
                          lot.containsKey('_id') &&
                          lot['_id'] != null &&
                          lot.containsKey('user') &&
                          lot['user'] != null,
                    )
                    .toList();

            // Log pour le débogage
            if (rawLots.length != _pendingLots.length) {
              ErrorHandler.instance.logInfo(
                'Lots filtrés: ${rawLots.length - _pendingLots.length} lots invalides supprimés',
                context: 'PendingLotsScreen',
              );
            }
          } else if (response['data'] is Map) {
            // Si c'est un Map, vérifier qu'il contient des données valides
            final lotMap = Map<String, dynamic>.from(response['data']);
            if (lotMap.containsKey('_id') &&
                lotMap['_id'] != null &&
                lotMap.containsKey('user') &&
                lotMap['user'] != null) {
              _pendingLots = [lotMap];
            } else {
              _pendingLots = [];
              ErrorHandler.instance.logInfo(
                'Lot unique filtré car invalide',
                context: 'PendingLotsScreen',
              );
            }
          } else {
            // Si ce n'est ni une liste ni un Map, initialiser une liste vide
            _pendingLots = [];
          }

          ErrorHandler.instance.logInfo(
            'Lots en attente chargés pour le vétérinaire ${user.id}: ${_pendingLots.length} lots',
            context: 'PendingLotsScreen',
          );

          // Afficher les détails des lots pour le débogage
          if (_pendingLots.isNotEmpty) {
            for (var lot in _pendingLots) {
              ErrorHandler.instance.logInfo(
                'Lot en attente: ID=${lot['_id']}, Espèce=${lot['espece'] != null ? (lot['espece']['nom'] ?? 'Inconnue') : 'Inconnue'}, Vétérinaire=${lot['veterinaire'] != null ? lot['veterinaire']['_id'] : 'Non assigné'}',
                context: 'PendingLotsScreen',
              );
            }
          } else {
            ErrorHandler.instance.logInfo(
              'Aucun lot en attente pour ce vétérinaire',
              context: 'PendingLotsScreen',
            );
          }
        } else {
          ErrorHandler.instance.logWarning(
            'Réponse reçue sans données: $response',
            context: 'PendingLotsScreen',
          );
          _pendingLots = [];
        }
      } catch (e) {
        ErrorHandler.instance.logError(
          'Erreur API lors de la récupération des lots: $e',
          context: 'PendingLotsScreen',
        );

        // Si c'est une erreur 404, on considère qu'il n'y a pas de lots en attente
        if (e.toString().contains('notFound') || e.toString().contains('404')) {
          _pendingLots = [];
          setState(() {
            _isLoading = false;
          });
          return;
        }
        throw Exception('Erreur lors de la récupération des lots: $e');
      }

      // Extraire les espèces disponibles et appliquer les filtres
      _extractAvailableSpecies();
      _applyFilters();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      ErrorHandler.instance.logError(
        'Erreur générale: $e',
        context: 'PendingLotsScreen',
      );
      setState(() {
        _errorMessage = 'Erreur lors du chargement: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  void _extractAvailableSpecies() {
    final species =
        _pendingLots
            .map((lot) {
              // Extraire le nom de l'espèce selon la structure des données
              if (lot['espece'] is Map) {
                return lot['espece']['nom'] ?? 'Inconnu';
              } else if (lot['espece'] is String) {
                return lot['espece'];
              } else if (lot['especeNom'] != null) {
                return lot['especeNom'];
              }
              return 'Inconnu';
            })
            .where((species) => species != 'Inconnu')
            .cast<String>()
            .toSet()
            .toList();
    species.sort();
    _availableSpecies = species;
  }

  void _applyFilters() {
    if (_selectedSpecies == null || _selectedSpecies!.isEmpty) {
      _filteredLots = List.from(_pendingLots);
    } else {
      _filteredLots =
          _pendingLots.where((lot) {
            String lotSpecies = 'Inconnu';
            if (lot['espece'] is Map) {
              lotSpecies = lot['espece']['nom'] ?? 'Inconnu';
            } else if (lot['espece'] is String) {
              lotSpecies = lot['espece'];
            } else if (lot['especeNom'] != null) {
              lotSpecies = lot['especeNom'];
            }
            return lotSpecies.toLowerCase() == _selectedSpecies!.toLowerCase();
          }).toList();
    }
  }

  Future<void> _approveLot(String lotId) async {
    try {
      final user = await UnifiedAuthService().getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non autorisé');
      }

      // Vérifier que l'utilisateur est bien un vétérinaire
      if (user is! Veterinaire) {
        throw Exception('L\'utilisateur n\'est pas un vétérinaire');
      }

      // Log l'action pour le débogage
      ErrorHandler.instance.logInfo(
        'Approbation du lot $lotId par l\'utilisateur ${user.id}',
        context: 'PendingLotsScreen',
      );

      // Afficher un indicateur de chargement
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Approbation du lot en cours...'),
            duration: Duration(seconds: 1),
          ),
        );
      }

      // Utiliser le service unifié pour approuver le lot
      final success = await UnifiedLotService().approveLot(lotId);

      if (success) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Lot approuvé et envoyé au maryeur'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );

        // Actualiser la liste des lots en attente
        await _loadPendingLots();
      } else {
        throw Exception('Échec de l\'approbation du lot');
      }
    } catch (e) {
      // Log l'erreur pour le débogage
      ErrorHandler.instance.logError(
        'Erreur lors de l\'approbation du lot: $e',
        context: 'PendingLotsScreen',
      );

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: ${e.toString()}'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 5),
        ),
      );
    }
  }

  Future<void> _rejectLot(String lotId) async {
    try {
      final user = await UnifiedAuthService().getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non autorisé');
      }

      // Vérifier que l'utilisateur est bien un vétérinaire
      if (user is! Veterinaire) {
        throw Exception('L\'utilisateur n\'est pas un vétérinaire');
      }

      // Log l'action pour le débogage
      ErrorHandler.instance.logInfo(
        'Rejet du lot $lotId par l\'utilisateur ${user.id}',
        context: 'PendingLotsScreen',
      );

      // Demander le motif du rejet
      final motif = await _showRejectReasonDialog();
      if (motif == null) {
        // L'utilisateur a annulé
        return;
      }

      // Afficher un indicateur de chargement
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Rejet du lot en cours...'),
            duration: Duration(seconds: 1),
          ),
        );
      }

      // Utiliser le service unifié pour rejeter le lot
      final success = await UnifiedLotService().rejectLot(lotId, motif: motif);

      if (success) {
        // Actualiser la liste
        await _loadPendingLots();

        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lot refusé avec le motif: $motif'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );
      } else {
        throw Exception('Échec du rejet du lot');
      }
    } catch (e) {
      // Log l'erreur pour le débogage
      ErrorHandler.instance.logError(
        'Erreur lors du rejet du lot: $e',
        context: 'PendingLotsScreen',
      );

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: ${e.toString()}'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  Future<String?> _showRejectReasonDialog() async {
    final TextEditingController controller = TextEditingController();
    return showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Motif du rejet'),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(
              hintText: 'Veuillez indiquer le motif du rejet',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(null),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                if (controller.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Veuillez indiquer un motif'),
                      backgroundColor: Colors.red,
                    ),
                  );
                } else {
                  Navigator.of(context).pop(controller.text.trim());
                }
              },
              child: const Text('Confirmer'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Lots en attente'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPendingLots,
            tooltip: 'Actualiser',
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Filtre par espèce
            if (_availableSpecies.isNotEmpty) _buildSpeciesFilter(),

            // Contenu principal
            Expanded(
              child:
                  _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : _errorMessage != null
                      ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 48,
                              color: Theme.of(context).colorScheme.error,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _errorMessage!,
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.error,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 24),
                            ElevatedButton(
                              onPressed: _loadPendingLots,
                              child: const Text('Réessayer'),
                            ),
                          ],
                        ),
                      )
                      : _filteredLots.isEmpty
                      ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.check_circle_outline,
                              size: 64,
                              color: Colors.green[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _selectedSpecies != null
                                  ? 'Aucun lot en attente pour "$_selectedSpecies"'
                                  : 'Aucun lot en attente',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _selectedSpecies != null
                                  ? 'Essayez de changer le filtre d\'espèce'
                                  : 'Tous les lots ont été traités',
                              style: TextStyle(color: Colors.grey[500]),
                            ),
                          ],
                        ),
                      )
                      : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _filteredLots.length,
                        itemBuilder: (context, index) {
                          final lot = _filteredLots[index];
                          return _buildLotCard(context, lot);
                        },
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpeciesFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(Icons.filter_list, color: Color(0xFF1565C0)),
          const SizedBox(width: 12),
          const Text(
            'Filtrer par espèce:',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Color(0xFF1565C0),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _selectedSpecies,
              decoration: InputDecoration(
                hintText: 'Toutes les espèces',
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
              ),
              items: [
                const DropdownMenuItem<String>(
                  value: null,
                  child: Text('Toutes les espèces'),
                ),
                ..._availableSpecies.map(
                  (species) => DropdownMenuItem<String>(
                    value: species,
                    child: Text(species),
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedSpecies = value;
                  _applyFilters();
                });
              },
            ),
          ),
          if (_selectedSpecies != null) ...[
            const SizedBox(width: 8),
            IconButton(
              onPressed: () {
                setState(() {
                  _selectedSpecies = null;
                  _applyFilters();
                });
              },
              icon: const Icon(Icons.clear, color: Colors.grey),
              tooltip: 'Effacer le filtre',
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLotCard(BuildContext context, Map<String, dynamic> lot) {
    final espece = lot['espece'] ?? 'Inconnu';
    final date =
        lot['dateTest'] != null
            ? DateFormat('dd/MM/yyyy').format(DateTime.parse(lot['dateTest']))
            : 'Date inconnue';
    final photoPath = lot['photo'];

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image and basic info
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                ),
                child:
                    photoPath != null && photoPath.isNotEmpty
                        ? Image.network(
                          UnifiedApiService().getImageUrl(photoPath),
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 120,
                              height: 120,
                              color: Colors.grey[300],
                              child: Icon(
                                Icons.image_not_supported,
                                size: 40,
                                color: Colors.grey[500],
                              ),
                            );
                          },
                        )
                        : Container(
                          width: 120,
                          height: 120,
                          color: Colors.grey[300],
                          child: Icon(
                            Icons.image_not_supported,
                            size: 40,
                            color: Colors.grey[500],
                          ),
                        ),
              ),

              // Info
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        espece,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Date: $date',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Quantité: ${lot['quantite'] ?? 'N/A'} | Poids: ${lot['poids'] ?? 'N/A'} kg',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Température: ${lot['temperature'] ?? 'N/A'} °C',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          // Divider
          const Divider(),

          // Actions
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: TextButton.icon(
                    onPressed: () {
                      // View details action
                      _showLotDetails(context, lot);
                    },
                    icon: const Icon(Icons.visibility),
                    label: const Text('Détails'),
                  ),
                ),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _approveLot(lot['_id'] ?? lot['id']),
                    icon: const Icon(Icons.check),
                    label: const Text('Approuver'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                    ),
                  ),
                ),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _rejectLot(lot['_id'] ?? lot['id']),
                    icon: const Icon(Icons.close, color: Colors.red),
                    label: const Text(
                      'Refuser',
                      style: TextStyle(color: Colors.red),
                    ),
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Colors.red),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showLotDetails(BuildContext context, Map<String, dynamic> lot) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Détails - ${lot['espece'] ?? 'Inconnu'}'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (lot['photo'] != null && lot['photo'].isNotEmpty) ...[
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        UnifiedApiService().getImageUrl(lot['photo']),
                        width: double.infinity,
                        height: 200,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: double.infinity,
                            height: 200,
                            color: Colors.grey[300],
                            child: Icon(
                              Icons.image_not_supported,
                              size: 40,
                              color: Colors.grey[500],
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                  _buildDetailItem('Identifiant', lot['identifiant'] ?? 'N/A'),
                  _buildDetailItem('Espèce', lot['espece'] ?? 'N/A'),
                  _buildDetailItem(
                    'Quantité',
                    lot['quantite']?.toString() ?? 'N/A',
                  ),
                  _buildDetailItem('Poids', '${lot['poids'] ?? 'N/A'} kg'),
                  _buildDetailItem(
                    'Température',
                    '${lot['temperature'] ?? 'N/A'} °C',
                  ),
                  _buildDetailItem(
                    'Date de soumission',
                    lot['dateSoumission'] ?? 'N/A',
                  ),

                  const SizedBox(height: 16),
                  const Text(
                    'Décision:',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.of(context).pop();
                            _approveLot(lot['_id'] ?? lot['id']);
                          },
                          icon: const Icon(Icons.check),
                          label: const Text('Approuver'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            Navigator.of(context).pop();
                            _rejectLot(lot['_id'] ?? lot['id']);
                          },
                          icon: const Icon(Icons.close, color: Colors.red),
                          label: const Text(
                            'Refuser',
                            style: TextStyle(color: Colors.red),
                          ),
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: Colors.red),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('$label: ', style: const TextStyle(fontWeight: FontWeight.bold)),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
