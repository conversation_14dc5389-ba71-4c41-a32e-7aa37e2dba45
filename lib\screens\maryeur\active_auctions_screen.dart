import 'package:flutter/material.dart';
import '../../services/unified_api_service.dart';
import '../../services/unified_auth_service.dart';
import '../../services/unified_lot_service.dart';
import '../../models/maryeur.dart';
import '../../utils/error_handler.dart';
import '../../widgets/countdown_timer.dart';
import 'auction_detail_screen.dart';
import 'extend_auction_dialog.dart';

class ActiveAuctionsScreen extends StatefulWidget {
  const ActiveAuctionsScreen({super.key});

  @override
  State<ActiveAuctionsScreen> createState() => _ActiveAuctionsScreenState();
}

class _ActiveAuctionsScreenState extends State<ActiveAuctionsScreen> {
  List<Map<String, dynamic>> _activeAuctions = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadActiveAuctions();
  }

  /// Affiche une boîte de dialogue de confirmation pour clôturer une enchère
  void _confirmEndAuction(Map<String, dynamic> auction) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Clôturer l\'enchère'),
            content: const Text(
              'Êtes-vous sûr de vouloir clôturer cette enchère ? '
              'Cette action est irréversible et attribuera le lot au dernier enchérisseur.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _endAuction(auction['_id'] ?? auction['id']);
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('Clôturer'),
              ),
            ],
          ),
    );
  }

  /// Affiche la boîte de dialogue pour prolonger une enchère
  void _showExtendAuctionDialog(Map<String, dynamic> auction) {
    final lot = UnifiedLotService().getLotFromMap(auction);
    if (lot == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Erreur: Impossible de récupérer les détails du lot'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder:
          (context) =>
              ExtendAuctionDialog(lot: lot, onSuccess: _loadActiveAuctions),
    );
  }

  /// Clôture une enchère en cours
  Future<void> _endAuction(String auctionId) async {
    try {
      // Afficher un indicateur de chargement
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Clôture de l\'enchère en cours...'),
                ],
              ),
            ),
      );

      // Appeler le service pour clôturer l'enchère
      final success = await UnifiedLotService().endAuction(auctionId);

      // Fermer la boîte de dialogue de chargement
      if (mounted) Navigator.of(context).pop();

      if (success) {
        // Afficher un message de succès
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Enchère clôturée avec succès'),
              backgroundColor: Colors.green,
            ),
          );
        }

        // Recharger la liste des enchères actives
        _loadActiveAuctions();
      } else {
        // Afficher un message d'erreur
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Erreur lors de la clôture de l\'enchère'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Fermer la boîte de dialogue de chargement en cas d'erreur
      if (mounted) Navigator.of(context).pop();

      // Log l'erreur
      ErrorHandler.instance.logError(
        e,
        context: 'ActiveAuctionsScreen._endAuction',
      );

      // Afficher un message d'erreur
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadActiveAuctions() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final user = await UnifiedAuthService().getCurrentUser();
      if (user == null) {
        throw Exception('Utilisateur non autorisé');
      }

      // Vérifier que l'utilisateur est bien un maryeur
      if (user is! Maryeur) {
        throw Exception('L\'utilisateur n\'est pas un maryeur');
      }

      if (user.id != null) {
        try {
          final lots = await UnifiedLotService().getActiveAuctions();

          // Filtrer les lots pour ce maryeur
          final filteredLots =
              lots.where((lot) => lot.maryeurId == user.id).toList();

          // Convertir les lots en Map pour maintenir la compatibilité avec le code existant
          _activeAuctions = filteredLots.map((lot) => lot.toMap()).toList();

          setState(() {
            _isLoading = false;
          });
        } catch (e) {
          ErrorHandler.instance.logError(
            e,
            context: 'ActiveAuctionsScreen._loadActiveAuctions',
          );
          throw Exception('Erreur lors de la récupération des enchères: $e');
        }
      } else {
        setState(() {
          _activeAuctions = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      ErrorHandler.instance.logError(
        e,
        context: 'ActiveAuctionsScreen._loadActiveAuctions',
      );
      setState(() {
        _errorMessage = 'Erreur lors du chargement: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Enchères actives'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadActiveAuctions,
            tooltip: 'Actualiser',
          ),
        ],
      ),
      body: SafeArea(
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _errorMessage != null
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Theme.of(context).colorScheme.error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.error,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _loadActiveAuctions,
                        child: const Text('Réessayer'),
                      ),
                    ],
                  ),
                )
                : _activeAuctions.isEmpty
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.gavel, size: 80, color: Colors.grey[400]),
                      const SizedBox(height: 16),
                      Text(
                        'Aucune enchère active',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Les enchères que vous avez initiées apparaîtront ici',
                        style: TextStyle(color: Colors.grey[600]),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
                : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _activeAuctions.length,
                  itemBuilder: (context, index) {
                    final auction = _activeAuctions[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Image
                          Container(
                            height: 150,
                            width: double.infinity,
                            color: Colors.grey[300],
                            child:
                                auction['photo'] != null &&
                                        auction['photo'].toString().isNotEmpty
                                    ? Image.network(
                                      UnifiedApiService().getImageUrl(
                                        auction['photo'],
                                      ),
                                      fit: BoxFit.cover,
                                      errorBuilder: (
                                        context,
                                        error,
                                        stackTrace,
                                      ) {
                                        return Center(
                                          child: Icon(
                                            Icons.image_not_supported,
                                            size: 50,
                                            color: Colors.grey[500],
                                          ),
                                        );
                                      },
                                    )
                                    : Center(
                                      child: Icon(
                                        Icons.image,
                                        size: 50,
                                        color: Colors.grey[500],
                                      ),
                                    ),
                          ),

                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Title and price
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      auction['espece'] ?? 'Poisson',
                                      style: Theme.of(
                                        context,
                                      ).textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 6,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).primaryColor,
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Text(
                                        '${auction['prixinitial'] ?? '0'} TND',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),

                                // Details
                                Row(
                                  children: [
                                    _buildDetailItem(
                                      Icons.scale,
                                      'Poids',
                                      '${auction['poid'] ?? '0'} kg',
                                    ),
                                    const SizedBox(width: 16),
                                    _buildDetailItem(
                                      Icons.inventory_2,
                                      'Quantité',
                                      auction['quantite'] ?? '0',
                                    ),
                                    const SizedBox(width: 16),
                                    _buildDetailItem(
                                      Icons.thermostat,
                                      'Temp.',
                                      '${auction['temperature'] ?? '0'}°C',
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),

                                // Current bid
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const Text(
                                            'Enchère actuelle',
                                            style: TextStyle(
                                              color: Colors.grey,
                                            ),
                                          ),
                                          Text(
                                            '${auction['current'] ?? auction['prixinitial'] ?? '0'} TND',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 18,
                                            ),
                                          ),
                                          if (auction['dateFinEnchere'] != null)
                                            Row(
                                              children: [
                                                const Text(
                                                  'Temps restant: ',
                                                  style: TextStyle(
                                                    color: Colors.grey,
                                                    fontSize: 12,
                                                  ),
                                                ),
                                                CountdownTimer(
                                                  endTime: DateTime.parse(
                                                    auction['dateFinEnchere'],
                                                  ),
                                                  textStyle: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 12,
                                                  ),
                                                  warningColor: Colors.orange,
                                                  expiredColor: Colors.red,
                                                  onFinished: () {
                                                    // Rafraîchir la liste lorsque le compte à rebours est terminé
                                                    if (mounted) {
                                                      _loadActiveAuctions();
                                                    }
                                                  },
                                                ),
                                              ],
                                            ),
                                        ],
                                      ),
                                      Row(
                                        children: [
                                          ElevatedButton(
                                            onPressed: () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder:
                                                      (context) =>
                                                          MaryeurAuctionDetailScreen(
                                                            lotId:
                                                                auction['_id'],
                                                          ),
                                                ),
                                              ).then(
                                                (_) => _loadActiveAuctions(),
                                              );
                                            },
                                            child: const Text('Voir détails'),
                                          ),
                                          const SizedBox(width: 8),
                                          ElevatedButton(
                                            onPressed:
                                                () => _showExtendAuctionDialog(
                                                  auction,
                                                ),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.blue,
                                            ),
                                            child: const Text('Prolonger'),
                                          ),
                                          const SizedBox(width: 8),
                                          ElevatedButton(
                                            onPressed:
                                                () =>
                                                    _confirmEndAuction(auction),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.red,
                                            ),
                                            child: const Text('Clôturer'),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
      ),
    );
  }

  Widget _buildDetailItem(IconData icon, String label, String value) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}
