const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3005/api';

// Données de test pour un pêcheur
const testPecheur = {
  email: 'z<PERSON><EMAIL>',
  password: '<PERSON><PERSON><PERSON>er1*'
};

// Fonction pour se connecter et obtenir un token
async function login() {
  try {
    console.log('🔐 Connexion du pêcheur...');
    const response = await axios.post(`${BASE_URL}/auth/login`, testPecheur);
    
    if (response.data && response.data.token) {
      console.log('✅ Connexion réussie');
      return {
        token: response.data.token,
        user: response.data.user
      };
    } else {
      throw new Error('Pas de token dans la réponse');
    }
  } catch (error) {
    console.error('❌ Erreur de connexion:', error.response?.data || error.message);
    throw error;
  }
}

// Test final complet
async function testFinal() {
  try {
    console.log('🚀 Test final complet\n');
    
    // 1. Se connecter
    const { token, user } = await login();
    console.log(`Utilisateur connecté: ${user.prenom} ${user.nom} (${user.roles})\n`);
    
    // 2. Tester vétérinaires
    console.log('🏥 Test vétérinaires...');
    const vetResponse = await axios.get(`${BASE_URL}/veterinaires`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('Structure réponse vétérinaires:');
    console.log(`- success: ${vetResponse.data.success}`);
    console.log(`- message: ${vetResponse.data.message}`);
    console.log(`- data type: ${typeof vetResponse.data.data}`);
    
    const vetData = vetResponse.data.data?.data || vetResponse.data.data || [];
    console.log(`- Nombre de vétérinaires: ${vetData.length}`);
    
    if (vetData.length > 0) {
      console.log('Premier vétérinaire:');
      const firstVet = vetData[0];
      console.log(`  - Nom: ${firstVet.prenom} ${firstVet.nom}`);
      console.log(`  - Email: ${firstVet.email}`);
      console.log(`  - ID type: ${typeof firstVet.id}`);
      console.log(`  - ID structure:`, firstVet.id);
      console.log(`  - Validé: ${firstVet.isValidated}`);
    }
    
    // 3. Tester mareyeurs
    console.log('\n🐟 Test mareyeurs...');
    const marResponse = await axios.get(`${BASE_URL}/maryeurs`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('Structure réponse mareyeurs:');
    console.log(`- success: ${marResponse.data.success}`);
    console.log(`- message: ${marResponse.data.message}`);
    console.log(`- data type: ${typeof marResponse.data.data}`);
    
    const marData = marResponse.data.data?.data || marResponse.data.data || [];
    console.log(`- Nombre de mareyeurs: ${marData.length}`);
    
    if (marData.length > 0) {
      console.log('Premier mareyeur:');
      const firstMar = marData[0];
      console.log(`  - Nom: ${firstMar.prenom} ${firstMar.nom}`);
      console.log(`  - Email: ${firstMar.email}`);
      console.log(`  - ID type: ${typeof firstMar.id}`);
      console.log(`  - ID structure:`, firstMar.id);
      console.log(`  - Validé: ${firstMar.isValidated}`);
    }
    
    // 4. Tester création de lot si possible
    if (vetData.length > 0 && marData.length > 0) {
      console.log('\n📦 Test création de lot...');
      
      // Convertir les IDs buffer en string
      function convertBufferId(id) {
        if (typeof id === 'string') return id;
        if (id && id.buffer) {
          const bytes = [];
          for (let i = 0; i < 12; i++) {
            bytes.push(id.buffer[i.toString()]);
          }
          return bytes.map(b => b.toString(16).padStart(2, '0')).join('');
        }
        return id.toString();
      }
      
      const vetId = convertBufferId(vetData[0].id);
      const marId = convertBufferId(marData[0].id);
      
      console.log(`Vétérinaire sélectionné: ${vetData[0].prenom} ${vetData[0].nom} (ID: ${vetId})`);
      console.log(`Mareyeur sélectionné: ${marData[0].prenom} ${marData[0].nom} (ID: ${marId})`);
      
      const lotData = {
        identifiant: `LOT-FINAL-TEST-${Date.now()}`,
        nom: 'Lot de test final',
        photo: 'test-final.jpg',
        poids: 2.0,
        temperature: 3,
        quantite: 1,
        especeNom: 'Rouget',
        engin: 'Filet',
        zone: 'Zone test final',
        lieu: 'Sfax',
        veterinaire: vetId,
        maryeur: marId,
        status: false,
        test: false,
        vendu: false,
        isValidated: false
      };
      
      const lotResponse = await axios.post(`${BASE_URL}/lots`, lotData, {
        headers: { 
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Lot créé avec succès!');
      console.log(`- ID du lot: ${lotResponse.data.lot?.id || 'N/A'}`);
      console.log(`- Identifiant: ${lotResponse.data.lot?.identifiant || 'N/A'}`);
    }
    
    console.log('\n🎉 Test final terminé avec succès!');
    console.log('\n📋 Résumé:');
    console.log(`✅ Connexion: OK`);
    console.log(`✅ Vétérinaires: ${vetData.length} trouvés`);
    console.log(`✅ Mareyeurs: ${marData.length} trouvés`);
    console.log(`✅ Création de lot: ${vetData.length > 0 && marData.length > 0 ? 'OK' : 'Pas testé'}`);
    
    console.log('\n🔧 Instructions pour l\'application mobile:');
    console.log('1. La structure de réponse est: {success: true, data: {data: [...]}}');
    console.log('2. Les IDs sont dans un format buffer qui doit être converti en string');
    console.log('3. Le backend fonctionne correctement');
    console.log('4. L\'application mobile doit extraire response.data.data.data pour obtenir les vraies données');
    
  } catch (error) {
    console.error('\n💥 Test échoué:', error.message);
    if (error.response?.data) {
      console.error('Détails:', error.response.data);
    }
    process.exit(1);
  }
}

// Exécuter le test
testFinal();
