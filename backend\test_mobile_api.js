const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3005/api';

// Fonction pour convertir les IDs buffer en string (comme dans l'app mobile)
function convertIdToString(id) {
  if (typeof id === 'string') {
    return id;
  } else if (id && typeof id === 'object' && id.buffer) {
    try {
      // Convertir le buffer en ObjectId string
      const buffer = id.buffer;
      const bytes = [];
      for (let i = 0; i < 12; i++) {
        bytes.push(buffer[i.toString()]);
      }
      return bytes.map(b => b.toString(16).padStart(2, '0')).join('');
    } catch (e) {
      console.log('Erreur conversion buffer ID:', e);
      return id.toString();
    }
  } else {
    return id ? id.toString() : '';
  }
}

// Données de test pour un pêcheur
const testPecheur = {
  email: '<EMAIL>',
  password: 'Zouhaier1*'
};

// Fonction pour se connecter et obtenir un token
async function login() {
  try {
    console.log('🔐 Connexion du pêcheur...');
    const response = await axios.post(`${BASE_URL}/auth/login`, testPecheur);

    if (response.data && response.data.token) {
      console.log('✅ Connexion réussie');
      return {
        token: response.data.token,
        user: response.data.user
      };
    } else {
      throw new Error('Pas de token dans la réponse');
    }
  } catch (error) {
    console.error('❌ Erreur de connexion:', error.response?.data || error.message);
    throw error;
  }
}

// Fonction pour tester la récupération des vétérinaires (comme le fait l'app mobile)
async function testVeterinaires(token) {
  try {
    console.log('\n🏥 Test récupération vétérinaires (simulation app mobile)...');

    const response = await axios.get(`${BASE_URL}/veterinaires`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    console.log('📋 Structure de la réponse vétérinaires:');
    console.log('- response.data.success:', response.data.success);
    console.log('- response.data.message:', response.data.message);
    console.log('- response.data.data type:', typeof response.data.data);
    console.log('- response.data.data.length:', response.data.data?.length);
    console.log('- response.data.data keys:', Object.keys(response.data.data || {}));
    console.log('- Réponse complète:', JSON.stringify(response.data, null, 2));

    // Simuler le traitement de l'app mobile - STRUCTURE CORRECTE
    const rawData = response.data.data?.data || response.data.data || [];
    console.log('\n🔍 Analyse des données brutes:');
    console.log('- Nombre d\'éléments bruts:', rawData.length);

    if (rawData.length > 0) {
      console.log('- Premier élément:', JSON.stringify(rawData[0], null, 2));

      // Filtrer comme le fait l'app mobile
      const filteredData = rawData.filter(item =>
        item &&
        typeof item === 'object' &&
        (item.isValidated === true || item.isValid === true) &&
        (item.isBlocked === false || item.isBlocked === null)
      );

      console.log('- Nombre après filtrage:', filteredData.length);

      // Convertir les IDs comme le fait l'app mobile
      const processedVets = [];
      for (const item of filteredData) {
        const id = convertIdToString(item._id || item.id);
        const nom = item.nom?.toString() || '';
        const prenom = item.prenom?.toString() || '';

        console.log(`🔍 Traitement vétérinaire: ${prenom} ${nom}`);
        console.log(`   - ID brut:`, item._id || item.id);
        console.log(`   - ID converti: ${id}`);

        if (id && id.length > 0 && nom.length > 0 && prenom.length > 0) {
          const vetMap = {
            _id: id,
            nom: nom,
            prenom: prenom,
            email: item.email?.toString() || '',
            telephone: item.telephone?.toString() || '',
            port: item.port?.toString() || '',
            matricule: item.matricule?.toString() || '',
            isValidated: item.isValidated === true || item.isValid === true,
          };

          processedVets.push(vetMap);
          console.log(`✅ Vétérinaire traité: ${prenom} ${nom} (ID: ${id})`);
        } else {
          console.log(`❌ Vétérinaire ignoré: données manquantes`);
        }
      }

      console.log(`\n📊 Résultat final: ${processedVets.length} vétérinaires utilisables`);
      return processedVets;
    }

    return [];
  } catch (error) {
    console.error('❌ Erreur récupération vétérinaires:', error.response?.data || error.message);
    return [];
  }
}

// Fonction pour tester la récupération des mareyeurs (comme le fait l'app mobile)
async function testMareyeurs(token) {
  try {
    console.log('\n🐟 Test récupération mareyeurs (simulation app mobile)...');

    const response = await axios.get(`${BASE_URL}/maryeurs`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    console.log('📋 Structure de la réponse mareyeurs:');
    console.log('- response.data.success:', response.data.success);
    console.log('- response.data.message:', response.data.message);
    console.log('- response.data.data type:', typeof response.data.data);
    console.log('- response.data.data.length:', response.data.data?.length);

    // Simuler le traitement de l'app mobile - STRUCTURE CORRECTE
    const rawData = response.data.data?.data || response.data.data || [];
    console.log('\n🔍 Analyse des données brutes:');
    console.log('- Nombre d\'éléments bruts:', rawData.length);

    if (rawData.length > 0) {
      console.log('- Premier élément:', JSON.stringify(rawData[0], null, 2));

      // Filtrer comme le fait l'app mobile
      const filteredData = rawData.filter(item =>
        item &&
        typeof item === 'object' &&
        (item.isValidated === true || item.isValid === true) &&
        (item.isBlocked === false || item.isBlocked === null)
      );

      console.log('- Nombre après filtrage:', filteredData.length);

      // Convertir les IDs comme le fait l'app mobile
      const processedMars = [];
      for (const item of filteredData) {
        const id = convertIdToString(item._id || item.id);
        const nom = item.nom?.toString() || '';
        const prenom = item.prenom?.toString() || '';

        console.log(`🔍 Traitement mareyeur: ${prenom} ${nom}`);
        console.log(`   - ID brut:`, item._id || item.id);
        console.log(`   - ID converti: ${id}`);

        if (id && id.length > 0 && nom.length > 0 && prenom.length > 0) {
          const marMap = {
            _id: id,
            nom: nom,
            prenom: prenom,
            email: item.email?.toString() || '',
            telephone: item.telephone?.toString() || '',
            port: item.port?.toString() || '',
            matricule: item.matricule?.toString() || '',
            isValidated: item.isValidated === true || item.isValid === true,
          };

          processedMars.push(marMap);
          console.log(`✅ Mareyeur traité: ${prenom} ${nom} (ID: ${id})`);
        } else {
          console.log(`❌ Mareyeur ignoré: données manquantes`);
        }
      }

      console.log(`\n📊 Résultat final: ${processedMars.length} mareyeurs utilisables`);
      return processedMars;
    }

    return [];
  } catch (error) {
    console.error('❌ Erreur récupération mareyeurs:', error.response?.data || error.message);
    return [];
  }
}

// Fonction pour tester la création d'un lot (simulation app mobile)
async function testLotCreation(token, user, veterinaires, mareyeurs) {
  try {
    console.log('\n📦 Test création lot (simulation app mobile)...');

    if (veterinaires.length === 0) {
      throw new Error('Aucun vétérinaire disponible');
    }

    if (mareyeurs.length === 0) {
      throw new Error('Aucun mareyeur disponible');
    }

    const selectedVet = veterinaires[0];
    const selectedMar = mareyeurs[0];

    console.log('📋 Sélection:');
    console.log(`- Vétérinaire: ${selectedVet.prenom} ${selectedVet.nom} (ID: ${selectedVet._id})`);
    console.log(`- Mareyeur: ${selectedMar.prenom} ${selectedMar.nom} (ID: ${selectedMar._id})`);

    // Préparer les données comme le fait l'app mobile
    const lotData = {
      identifiant: `LOT-MOBILE-TEST-${Date.now()}`,
      nom: 'Lot de test mobile',
      photo: 'test-mobile.jpg',
      poids: 3.0,
      temperature: 5,
      quantite: 1,
      especeNom: 'Rouget',
      engin: 'Filet',
      zone: 'Zone mobile test',
      lieu: 'Sfax',
      veterinaire: selectedVet._id,
      maryeur: selectedMar._id,
      status: false,
      test: false,
      vendu: false,
      isValidated: false
    };

    console.log('\n📤 Envoi des données lot:');
    console.log(JSON.stringify(lotData, null, 2));

    const response = await axios.post(`${BASE_URL}/lots`, lotData, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('\n✅ Lot créé avec succès!');
    console.log('📋 Réponse serveur:');
    console.log('- success:', response.data.success);
    console.log('- message:', response.data.message);
    console.log('- data.id:', response.data.data?.id);
    console.log('- data.identifiant:', response.data.data?.identifiant);

    return response.data;
  } catch (error) {
    console.error('❌ Erreur création lot:', error.response?.data || error.message);
    throw error;
  }
}

// Fonction principale
async function main() {
  try {
    console.log('🚀 Test simulation app mobile\n');

    // 1. Se connecter
    const { token, user } = await login();

    // 2. Tester récupération vétérinaires
    const veterinaires = await testVeterinaires(token);

    // 3. Tester récupération mareyeurs
    const mareyeurs = await testMareyeurs(token);

    // 4. Tester création de lot
    if (veterinaires.length > 0 && mareyeurs.length > 0) {
      await testLotCreation(token, user, veterinaires, mareyeurs);
    } else {
      console.log('\n⚠️ Impossible de tester la création de lot: pas assez de données');
    }

    console.log('\n🎉 Test terminé avec succès!');

  } catch (error) {
    console.error('\n💥 Test échoué:', error.message);
    process.exit(1);
  }
}

// Exécuter le test
main();
